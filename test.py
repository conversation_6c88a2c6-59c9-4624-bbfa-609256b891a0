# Arquivo de teste para Monaco Editor
# Este arquivo demonstra syntax highlighting Python

import os
import json
from datetime import datetime

class TestMonaco:
    def __init__(self, name):
        self.name = name
        self.created_at = datetime.now()
    
    def hello(self):
        """Função de teste"""
        print(f"Hello from {self.name}!")
        return True
    
    def process_data(self, data):
        # Processar dados
        result = []
        for item in data:
            if isinstance(item, dict):
                result.append(item.get('value', 0))
        return result

if __name__ == "__main__":
    monaco = TestMonaco("Lilith Monaco Editor")
    monaco.hello()
    
    # Teste com dados
    test_data = [
        {"value": 10},
        {"value": 20},
        {"value": 30}
    ]
    
    print("Resultado:", monaco.process_data(test_data))