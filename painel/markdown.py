#!/usr/bin/env python3
"""
markdown.py - Sistema Superior de Formatação Markdown
"""

import re
import html

class MarkdownFormatter:
    def __init__(self):
        self.class_prefix = 'md'
    
    def format(self, text: str) -> str:
        """Formata markdown com classes CSS"""
        if not text:
            return ""
        
        # Escapar HTML primeiro
        text = html.escape(text)
        
        # Processar elementos
        text = self._process_code_blocks(text)
        text = self._process_headers(text)
        text = self._process_bold_italic(text)
        text = self._process_inline_code(text)
        text = self._process_links(text)
        text = self._process_lists(text)
        text = self._process_blockquotes(text)
        text = self._process_paragraphs(text)
        
        return f'<div class="{self.class_prefix}-content">{text}</div>'
    
    def _process_headers(self, text: str) -> str:
        """Processa headers"""
        pattern = re.compile(r'^(#{1,6})\s+(.+)$', re.MULTILINE)
        def replace_header(match):
            level = len(match.group(1))
            content = match.group(2)
            return f'<h{level} class="{self.class_prefix}-h{level}">{content}</h{level}>'
        return pattern.sub(replace_header, text)
    
    def _process_code_blocks(self, text: str) -> str:
        """Processa blocos de código"""
        pattern = re.compile(r'```(\w+)?\n(.*?)```', re.DOTALL)
        def replace_code_block(match):
            lang = match.group(1) or 'text'
            code = html.unescape(match.group(2))
            return f'<pre class="{self.class_prefix}-code-block"><code class="{self.class_prefix}-code {self.class_prefix}-lang-{lang}">{code}</code></pre>'
        return pattern.sub(replace_code_block, text)
    
    def _process_bold_italic(self, text: str) -> str:
        """Processa negrito e itálico"""
        # Bold + Italic
        text = re.sub(r'\*\*\*(.+?)\*\*\*', f'<strong class="{self.class_prefix}-bold-italic"><em>\\1</em></strong>', text)
        # Bold
        text = re.sub(r'\*\*(.+?)\*\*', f'<strong class="{self.class_prefix}-bold">\\1</strong>', text)
        # Italic
        text = re.sub(r'\*(.+?)\*', f'<em class="{self.class_prefix}-italic">\\1</em>', text)
        return text
    
    def _process_inline_code(self, text: str) -> str:
        """Processa código inline"""
        return re.sub(r'`([^`]+)`', f'<code class="{self.class_prefix}-inline-code">\\1</code>', text)
    
    def _process_links(self, text: str) -> str:
        """Processa links"""
        return re.sub(r'\[([^\]]+)\]\(([^)]+)\)', f'<a href="\\2" class="{self.class_prefix}-link">\\1</a>', text)
    
    def _process_lists(self, text: str) -> str:
        """Processa listas"""
        # Unordered lists
        text = re.sub(r'^(\s*)[*+-]\s+(.+)$', f'<div class="{self.class_prefix}-list-item"><span class="{self.class_prefix}-bullet">•</span><span class="{self.class_prefix}-list-content">\\2</span></div>', text, flags=re.MULTILINE)
        # Ordered lists
        text = re.sub(r'^(\s*)(\d+)\.\s+(.+)$', f'<div class="{self.class_prefix}-list-item"><span class="{self.class_prefix}-number">\\2.</span><span class="{self.class_prefix}-list-content">\\3</span></div>', text, flags=re.MULTILINE)
        return text
    
    def _process_blockquotes(self, text: str) -> str:
        """Processa citações"""
        return re.sub(r'^>\s+(.+)$', f'<blockquote class="{self.class_prefix}-blockquote">\\1</blockquote>', text, flags=re.MULTILINE)
    
    def _process_paragraphs(self, text: str) -> str:
        """Processa parágrafos"""
        lines = text.split('\n')
        result = []
        
        for line in lines:
            if line.strip():
                if not line.strip().startswith('<'):
                    result.append(f'<p class="{self.class_prefix}-paragraph">{line}</p>')
                else:
                    result.append(line)
            else:
                result.append('')
        
        return '\n'.join(result)


# Instância global
markdown_formatter = MarkdownFormatter()

def format_markdown(text: str) -> str:
    """Função principal para formatar markdown"""
    return markdown_formatter.format(text)