/*
 * sidebar.css - Sidebar com glow effects
 */

/* SIDEBAR CONTAINER */
#sidebar-container {
    width: 80px;
    background: #171717;
    border-right: 1px solid #333;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
}

/* SIDEBAR HEADER */
#sidebar-header {
    padding: 25px 0;
    background: #171717;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
}

/* LOGO */
#logo-container {
    width: 70px;
    height: 70px;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#logo-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

/* CONTAINER DOS BOTÕES */
#buttons-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
    width: 100%;
}

/* BOTÕES BASE */
.sidebar-button {
    width: 25px;
    height: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: block;
    object-fit: contain;
    border: none;
    background: none;
    filter: none;
}

/* PRIMEIROS 4 BOTÕES: GLOW VERMELHO */
.sidebar-button:nth-child(1):hover,
.sidebar-button:nth-child(2):hover,
.sidebar-button:nth-child(3):hover,
.sidebar-button:nth-child(4):hover {
    filter:
        drop-shadow(0 0 5px #ff0000)
        drop-shadow(0 0 10px #ff0000)
        drop-shadow(0 0 20px #ff0000)
        drop-shadow(0 0 40px #ff0000);
    transform: scale(1.2);
}

/* ÚLTIMOS 4 BOTÕES: GLOW BRANCO */
.sidebar-button:nth-child(5):hover,
.sidebar-button:nth-child(6):hover,
.sidebar-button:nth-child(7):hover,
.sidebar-button:nth-child(8):hover {
    filter:
        drop-shadow(0 0 5px #ffffff)
        drop-shadow(0 0 10px #ffffff)
        drop-shadow(0 0 20px #ffffff)
        drop-shadow(0 0 40px #ffffff);
    transform: scale(1.2);
}
