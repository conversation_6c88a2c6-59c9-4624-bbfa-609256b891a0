#!/usr/bin/env python3
"""
Painel.py - Interface exata do chat provisório
"""

from fastapi import FastAPI, HTTPException, Request, APIRouter, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
import requests
from typing import Optional, List, Dict, Set
from datetime import datetime
import os
import json
import asyncio
from pathlib import Path

# SPRINT 5 - BACKEND INTEGRATION
try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    WATCHDOG_AVAILABLE = True
except ImportError:
    print("[WATCHDOG] Módulo não encontrado - file watching desabilitado")
    WATCHDOG_AVAILABLE = False

# MARKDOWN INTEGRATION
try:
    from .markdown import format_markdown
except ImportError:
    try:
        from markdown import format_markdown
    except ImportError:
        print("[MARKDOWN] <PERSON><PERSON><PERSON><PERSON> não encontrado - markdown desabilitado")
        def format_markdown(text):
            return text

# POSTGRES INTEGRATION - In<PERSON>cio
try:
    import sys
    import os
    # Adicionar diretório pai ao Python path
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from database.db_manager import DatabaseManager
    db_manager = DatabaseManager()
    print("[POSTGRES] DatabaseManager carregado")
except Exception as e:
    db_manager = None
    print(f"[POSTGRES] DatabaseManager não disponível: {e}")
# POSTGRES INTEGRATION - Fim

# LOGS INTEGRATION - Início
try:
    import sys
    import os
    # Adicionar diretório pai ao Python path
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from logs.log_manager import log_manager
    print("[LOGS] LogManager carregado")
except Exception as e:
    log_manager = None
    print(f"[LOGS] LogManager não disponível: {e}")
# LOGS INTEGRATION - Fim

# AI PROVIDERS INTEGRATION - Início
try:
    from ai_providers.unified_ai import unified_ai
    print("[AI_PROVIDERS] Sistema unificado carregado")
    available_external = unified_ai.get_available_external_models()
    print(f"[AI_PROVIDERS] Modelos externos: {available_external}")

    # Debug das chaves de API
    import os
    from dotenv import load_dotenv
    load_dotenv()

    print(f"[AI_PROVIDERS] OpenAI Key: {'✅' if os.getenv('OPENAI_API_KEY') else '❌'}")
    print(f"[AI_PROVIDERS] Anthropic Key: {'✅' if os.getenv('ANTHROPIC_API_KEY') else '❌'}")
    print(f"[AI_PROVIDERS] Google Key: {'✅' if os.getenv('GOOGLE_API_KEY') else '❌'}")
    print(f"[AI_PROVIDERS] DeepSeek Key: {'✅' if os.getenv('DEEPSEEK_API_KEY') else '❌'}")

    # Função para reconectar AIs
    def reconnect_all_ais():
        """Reconecta todas as AIs externas"""
        global unified_ai
        try:
            # Reimportar e reinicializar
            import importlib
            import ai_providers.unified_ai
            importlib.reload(ai_providers.unified_ai)
            from ai_providers.unified_ai import UnifiedAI
            unified_ai = UnifiedAI()

            available = unified_ai.get_available_external_models()
            print(f"[AI_PROVIDERS] ✅ AIs reconectadas: {available}")
            return True, available
        except Exception as e:
            print(f"[AI_PROVIDERS] ❌ Erro ao reconectar: {e}")
            return False, []

except Exception as e:
    unified_ai = None
    print(f"[AI_PROVIDERS] Sistema não disponível: {e}")

    def reconnect_all_ais():
        return False, []
# AI PROVIDERS INTEGRATION - Fim

# FORMATTING INTEGRATION - Início (FACILMENTE REVERSÍVEL)
ENABLE_FORMATTING = False  # ← MUDE PARA False PARA DESABILITAR TUDO
try:
    if ENABLE_FORMATTING:
        from formatting.safe_formatter import safe_formatter
        print("[FORMATTING] Sistema seguro carregado")
    else:
        safe_formatter = None
        print("[FORMATTING] Formatação desabilitada por configuração")
except Exception as e:
    safe_formatter = None
    print(f"[FORMATTING] Sistema não disponível: {e}")
# FORMATTING INTEGRATION - Fim

# Imports dos módulos do sistema - REMOVIDOS PARA EVITAR CACHE
# from .chat1 import chat1_router as chat1_app
# from .chat1.chat1 import OllamaChat

# Implementação direta da classe OllamaChat para evitar imports problemáticos
class OllamaChat:
    def __init__(self, base_url: str = None):
        import os
        import platform
        
        if base_url:
            self.base_url = base_url
        elif os.getenv('OLLAMA_HOST'):
            self.base_url = os.getenv('OLLAMA_HOST')
        else:
            if platform.system() == "Windows":
                self.base_url = "http://localhost:11434"
            else:
                self.base_url = "http://***********:11434"
        
        self.conversation_history = []
    
    def check_connection(self) -> bool:
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def list_models(self):
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                data = response.json()
                return [model['name'] for model in data.get('models', [])]
            return []
        except:
            return []
    
    def send_message(self, message: str, model: str = "llama2"):
        try:
            payload = {
                "model": model,
                "prompt": message,
                "stream": False
            }
            
            response = requests.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=120
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('response', '')
            else:
                return f"Erro: {response.status_code}"
                
        except requests.exceptions.Timeout:
            return "Erro: Timeout na requisição"
        except requests.exceptions.ConnectionError:
            return "Erro: Não foi possível conectar ao Ollama"
        except Exception as e:
            return f"Erro: {str(e)}"

# Classes para o chat
class ChatMessage(BaseModel):
    message: str
    model: Optional[str] = "llama2"

class ChatResponse(BaseModel):
    response: str
    model: str

# Classes para multi-chat
class MultiChatMessage(BaseModel):
    message: str
    models: List[str]
    rounds: Optional[int] = 3  # Número de rodadas de conversa

class MultiChatResponse(BaseModel):
    conversation: List[dict]
    models: List[str]

# Configuração do FastAPI principal
app = FastAPI(
    title="Lilith Painel Enterprise",
    description="Sistema modular enterprise com Event Bus central",
    version="1.0.0"
)

# Middleware CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# I.5 - Error handling melhorado
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "status_code": exc.status_code,
            "timestamp": datetime.now().isoformat()
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": "Erro interno do servidor",
            "detail": str(exc),
            "timestamp": datetime.now().isoformat()
        }
    )

# Instância global do chat
import platform
if platform.system() == "Windows":
    chat_instance = OllamaChat("http://localhost:11434")
else:
    chat_instance = OllamaChat("http://***********:11434")
print(f"🔍 Painel: Configurado para conectar ao Ollama em {chat_instance.base_url}")

# Mount dos assets estáticos
import os
assets_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "painel", "assets")
if os.path.exists(assets_path):
    app.mount("/assets", StaticFiles(directory=assets_path), name="assets")

# Mount para arquivos markdown (CSS e JS)
static_path = os.path.dirname(os.path.abspath(__file__))
app.mount("/static", StaticFiles(directory=static_path), name="static")

@app.get("/", response_class=HTMLResponse)
async def get_main_interface():
    """Interface principal do painel - DESIGN EXATO DO CHAT PROVISÓRIO"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Lilith Chat - RESET TOTAL</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
        <meta http-equiv="Pragma" content="no-cache">
        <meta http-equiv="Expires" content="0">
        <link rel="stylesheet" href="/static/markdown.css">
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs/loader.js"></script>
        <style>
            /* RESET COMPLETO */
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            /* BODY - LAYOUT FULL SCREEN */
            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: #202020;
                color: #ffffff;
                height: 100vh;
                width: 100vw;
                overflow: hidden;
                display: flex;
                flex-direction: row;
            }

            /* SIDEBAR */
            .main-container {
                display: flex;
                height: 100vh;
                width: 100vw;
            }

            #sidebar-container {
                width: 80px;
                min-width: 80px;
                max-width: 80px;
                height: 100vh;
                background-color: #171717;
                border-right: 1px solid #333;
                display: flex;
                flex-direction: column;
                overflow: hidden;
                position: relative;
                z-index: 10;
            }

            .logo-area {
                position: absolute !important;
                top: 20px !important;
                left: 50% !important;
                transform: translateX(-50%) !important;
                width: 70px !important;
                height: 70px !important;
                z-index: 10 !important;
                margin-left: 0px !important;
            }

            #logo-container {
                width: 100% !important;
                height: 100% !important;
                border-radius: 12px !important;
                overflow: hidden !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            #logo-video {
                width: 100% !important;
                height: 100% !important;
                object-fit: cover !important;
                border-radius: 12px !important;
            }

            .buttons-area {
                position: absolute !important;
                top: 120px !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                display: flex !important;
                flex-direction: column !important;
                align-items: center !important;
                justify-content: flex-start !important;
                padding: 20px 0 !important;
                z-index: 5 !important;
            }

            #buttons-container {
                display: flex !important;
                flex-direction: column !important;
                align-items: center !important;
                gap: 18px !important;
                width: 100% !important;
                padding: 0 !important;
                margin: 0 !important;
            }

            .sidebar-button {
                width: 25px;
                height: 25px;
                object-fit: contain;
                border: none;
                background: none;
                box-shadow: none;
                outline: none;
                cursor: pointer;
                opacity: 0.8;
                filter: none;
                transition: all 0.3s ease;
                display: block;
            }

            .sidebar-button:hover {
                opacity: 1;
            }

            .btn-red:hover {
                filter: 
                    drop-shadow(0 0 2px #ff0000) 
                    drop-shadow(0 0 4px #ff0000) 
                    drop-shadow(0 0 6px #ff0000) 
                    drop-shadow(0 0 8px #ff0000) 
                    drop-shadow(0 0 12px #ff0000) 
                    drop-shadow(0 0 16px #ff0000) 
                    drop-shadow(0 0 24px rgba(255, 0, 0, 0.9)) 
                    drop-shadow(0 0 32px rgba(255, 0, 0, 0.8)) 
                    drop-shadow(0 0 48px rgba(255, 0, 0, 0.7)) 
                    drop-shadow(0 0 64px rgba(255, 0, 0, 0.6)) !important;
            }

            .btn-white:hover {
                filter: 
                    drop-shadow(0 0 2px #ffffff) 
                    drop-shadow(0 0 4px #ffffff) 
                    drop-shadow(0 0 6px #ffffff) 
                    drop-shadow(0 0 8px #ffffff) 
                    drop-shadow(0 0 12px #ffffff) 
                    drop-shadow(0 0 16px #ffffff) 
                    drop-shadow(0 0 24px rgba(255, 255, 255, 0.9)) 
                    drop-shadow(0 0 32px rgba(255, 255, 255, 0.8)) 
                    drop-shadow(0 0 48px rgba(255, 255, 255, 0.7)) 
                    drop-shadow(0 0 64px rgba(255, 255, 255, 0.6)) !important;
            }

            /* CHAT AREA */
            .chat-content {
                flex: 1;
                display: flex;
                flex-direction: column;
                background: transparent;
                height: 100vh;
                position: relative;
            }

            /* SELETOR DE MODELO - DISCRETO */
            .model-selector {
                position: absolute;
                top: 15px;
                right: 15px;
                background: #171717;
                border: 1px solid #333;
                color: #666;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 11px;
                font-family: 'Inter', sans-serif;
                z-index: 1000;
                outline: none;
                opacity: 0.7;
                transition: opacity 0.2s;
            }

            .model-selector:hover {
                opacity: 1;
                color: #999;
            }

            .model-selector:focus {
                opacity: 1;
                color: #ccc;
            }

            /* ÁREA DE MENSAGENS - TELA COMPLETA */
            .messages-area {
                flex: 1;
                width: 100%;
                height: 100%;
                overflow-y: auto;
                padding: 20px;
                padding-top: 60px;
                padding-right: 30px;
                padding-bottom: 200px;
                display: block;
                margin: 0;
                min-height: 0;
                position: relative;
            }

            /* CONTAINER INTERNO PARA CENTRALIZAR CONTEÚDO */
            .messages-inner {
                width: 100%;
                max-width: 900px;
                margin: 0 auto;
                display: flex;
                flex-direction: column;
                gap: 15px;
                padding-bottom: 150px;
            }

            /* MENSAGENS */
            .message {
                margin-bottom: 15px;
                padding: 15px;
                border-radius: 12px;
                max-width: 80%;
                word-wrap: break-word;
                box-sizing: border-box;
                font-size: 14px;
                line-height: 1.4;
            }

            .message.user {
                background: #171717;
                color: #ffffff;
                align-self: flex-end;
                margin-left: auto;
                border: none;
            }

            .message.assistant {
                background: #202020;
                color: #ffffff;
                border: none;
                align-self: flex-start;
                margin-right: auto;
            }

            /* FORMATAÇÃO SEGURA - FACILMENTE REMOVÍVEL */
            .ai-heading {
                font-weight: 700;
                font-size: 1.1em;
                color: #ff6b6b;
                margin: 8px 0 4px 0;
                border-left: 3px solid #ff6b6b;
                padding-left: 8px;
            }
            
            .ai-bold {
                font-weight: 600;
                color: #4ecdc4;
                margin: 4px 0;
            }
            
            .ai-code-marker {
                font-family: 'Courier New', monospace;
                background: #2a2a2a;
                color: #00ff00;
                padding: 4px 8px;
                border-radius: 4px;
                margin: 4px 0;
                border-left: 2px solid #00ff00;
            }
            
            .ai-list-item {
                margin: 2px 0 2px 16px;
                color: #e0e0e0;
            }
            
            .ai-numbered-item {
                margin: 2px 0 2px 8px;
                color: #e0e0e0;
                font-weight: 500;
            }
            
            .ai-normal {
                margin: 2px 0;
                color: #ffffff;
                line-height: 1.4;
            }
            
            .ai-break {
                height: 8px;
            }
            /* FIM FORMATAÇÃO SEGURA */

            /* INDICADOR DE DIGITAÇÃO - ESTILO CHATGPT */
            .typing-indicator {
                background: #202020;
                border: none;
                align-self: flex-start;
                margin-right: auto;
                margin-bottom: 15px;
                padding: 15px;
                border-radius: 12px;
                max-width: 80%;
                display: flex;
                align-items: center;
                min-height: 50px;
            }

            /* PONTO PULSANTE - MENOR MAS BRILHO INTENSO */
            .typing-dot {
                width: 4px;
                height: 4px;
                border-radius: 50%;
                background: #ff0000;
                box-shadow:
                    0 0 2px #ff0000,
                    0 0 4px #ff0000,
                    0 0 6px #ff0000,
                    0 0 8px #ff0000,
                    0 0 12px #ff0000,
                    0 0 16px #ff0000,
                    0 0 24px rgba(255, 0, 0, 0.9),
                    0 0 32px rgba(255, 0, 0, 0.8),
                    0 0 48px rgba(255, 0, 0, 0.7),
                    0 0 64px rgba(255, 0, 0, 0.6),
                    0 0 96px rgba(255, 0, 0, 0.4),
                    0 0 128px rgba(255, 0, 0, 0.2);
                animation: typing-pulse 2s ease-in-out infinite;
            }

            /* ANIMAÇÃO DE PULSAÇÃO COM BRILHO MÁXIMO */
            @keyframes typing-pulse {
                0% {
                    transform: scale(1);
                    background: #ff0000;
                    box-shadow:
                        0 0 2px #ff0000,
                        0 0 4px #ff0000,
                        0 0 6px #ff0000,
                        0 0 8px #ff0000,
                        0 0 12px #ff0000,
                        0 0 16px #ff0000,
                        0 0 24px rgba(255, 0, 0, 0.9),
                        0 0 32px rgba(255, 0, 0, 0.8),
                        0 0 48px rgba(255, 0, 0, 0.7),
                        0 0 64px rgba(255, 0, 0, 0.6),
                        0 0 96px rgba(255, 0, 0, 0.4),
                        0 0 128px rgba(255, 0, 0, 0.2);
                }
                25% {
                    transform: scale(1.2);
                    background: #ff4400;
                    box-shadow:
                        0 0 3px #ff4400,
                        0 0 6px #ff4400,
                        0 0 9px #ff4400,
                        0 0 12px #ff4400,
                        0 0 18px #ff4400,
                        0 0 24px #ff4400,
                        0 0 36px rgba(255, 68, 0, 0.9),
                        0 0 48px rgba(255, 68, 0, 0.8),
                        0 0 72px rgba(255, 68, 0, 0.7),
                        0 0 96px rgba(255, 68, 0, 0.6),
                        0 0 144px rgba(255, 68, 0, 0.4),
                        0 0 192px rgba(255, 68, 0, 0.2);
                }
                50% {
                    transform: scale(1.3);
                    background: #ff8800;
                    box-shadow:
                        0 0 4px #ff8800,
                        0 0 8px #ff8800,
                        0 0 12px #ff8800,
                        0 0 16px #ff8800,
                        0 0 24px #ff8800,
                        0 0 32px #ff8800,
                        0 0 48px rgba(255, 136, 0, 0.9),
                        0 0 64px rgba(255, 136, 0, 0.8),
                        0 0 96px rgba(255, 136, 0, 0.7),
                        0 0 128px rgba(255, 136, 0, 0.6),
                        0 0 192px rgba(255, 136, 0, 0.4),
                        0 0 256px rgba(255, 136, 0, 0.2);
                }
                75% {
                    transform: scale(1.2);
                    background: #ff4400;
                    box-shadow:
                        0 0 3px #ff4400,
                        0 0 6px #ff4400,
                        0 0 9px #ff4400,
                        0 0 12px #ff4400,
                        0 0 18px #ff4400,
                        0 0 24px #ff4400,
                        0 0 36px rgba(255, 68, 0, 0.9),
                        0 0 48px rgba(255, 68, 0, 0.8),
                        0 0 72px rgba(255, 68, 0, 0.7),
                        0 0 96px rgba(255, 68, 0, 0.6),
                        0 0 144px rgba(255, 68, 0, 0.4),
                        0 0 192px rgba(255, 68, 0, 0.2);
                }
                100% {
                    transform: scale(1);
                    background: #ff0000;
                    box-shadow:
                        0 0 2px #ff0000,
                        0 0 4px #ff0000,
                        0 0 6px #ff0000,
                        0 0 8px #ff0000,
                        0 0 12px #ff0000,
                        0 0 16px #ff0000,
                        0 0 24px rgba(255, 0, 0, 0.9),
                        0 0 32px rgba(255, 0, 0, 0.8),
                        0 0 48px rgba(255, 0, 0, 0.7),
                        0 0 64px rgba(255, 0, 0, 0.6),
                        0 0 96px rgba(255, 0, 0, 0.4),
                        0 0 128px rgba(255, 0, 0, 0.2);
                }
            }

            /* INPUT ÁREA */
            .input-area {
                position: fixed;
                bottom: 40px;
                left: 80px;
                right: 0;
                background: #202020;
                padding: 0;
                margin: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-shrink: 0;
            }

            /* CONTAINER INTERNO DO INPUT */
            .input-inner {
                width: 100%;
                max-width: 900px;
                display: flex;
                align-items: center;
                padding: 0;
                margin: 0;
            }

            .message-input {
                flex: 1;
                background: rgba(32, 32, 32, 0.7);
                backdrop-filter: blur(15px);
                -webkit-backdrop-filter: blur(15px);
                border: 1px solid rgba(255, 255, 255, 0.1);
                color: #fff;
                padding: 10px 15px;
                margin: 0;
                border-radius: 20px;
                font-size: 14px;
                font-family: 'Inter', sans-serif;
                outline: none;
                resize: none;
                height: 100px;
                min-height: 100px;
                max-height: 400px;
                transition: all 0.3s ease;
                overflow-y: auto;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            }

            .message-input:focus {
                border-color: rgba(255, 255, 255, 0.3);
                background: rgba(32, 32, 32, 0.8);
                backdrop-filter: blur(20px);
                -webkit-backdrop-filter: blur(20px);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1);
                transform: translateY(-1px);
            }

            /* SCROLLBAR CUSTOMIZADA */
            .messages-area::-webkit-scrollbar {
                width: 12px;
            }

            .messages-area::-webkit-scrollbar-track {
                background: rgba(0, 0, 0, 0.1);
            }

            .messages-area::-webkit-scrollbar-thumb {
                background: #444;
                border-radius: 6px;
                border: 2px solid transparent;
                background-clip: content-box;
            }

            .messages-area::-webkit-scrollbar-thumb:hover {
                background: #666;
                background-clip: content-box;
            }

            /* LOGS PANEL - Painel deslizante */
            .logs-panel {
                position: fixed;
                top: 0;
                left: 80px; /* Após o sidebar */
                right: 0;
                bottom: 0;
                background: #0a0a0a;
                border-left: 1px solid #333;
                z-index: 1;
                transform: translateX(-100%);
                transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                overflow: hidden;
                display: flex;
                flex-direction: column;
            }

            .logs-panel.active {
                transform: translateX(0);
            }

            /* MONACO EDITOR PANEL - Painel deslizante para editor */
            .monaco-panel {
                position: fixed;
                top: 0;
                left: 80px; /* Após o sidebar */
                right: 0;
                bottom: 0;
                background: #1e1e1e;
                border-left: 1px solid #333;
                z-index: 1;
                transform: translateX(-100%);
                transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                overflow: hidden;
                display: flex;
                flex-direction: column;
            }
            .monaco-panel.active {
                transform: translateX(0);
            }
            
            .monaco-container {
                display: flex;
                height: 100%;
                background: #1e1e1e;
            }
            
            /* Layout Principal - 3 Colunas */
            .main-workspace {
                display: flex;
                width: 100%;
                height: 100%;
                background: #1e1e1e;
            }

            /* Colunas Base */
            .workspace-column {
                display: flex;
                flex-direction: column;
                overflow: hidden;
                position: relative;
            }

            /* Explorer Column */
            .explorer-column {
                width: 250px;
                min-width: 200px;
                max-width: 400px;
                background: #252526;
                border-right: 1px solid #2d2d30;
            }

            /* Editor Column */
            .editor-column {
                flex: 1;
                min-width: 400px;
                background: #1e1e1e;
                display: flex;
                flex-direction: column;
            }

            /* Chat Column */
            .chat-column {
                width: 350px;
                min-width: 300px;
                max-width: 600px;
                background: #252526;
                border-left: 1px solid #2d2d30;
            }

            /* Dividers */
            .column-divider {
                width: 4px;
                background: #2d2d30;
                cursor: col-resize;
                position: relative;
                z-index: 10;
                transition: background 0.2s;
            }

            .column-divider:hover {
                background: #007acc;
            }

            /* Editor Header */
            .editor-header {
                height: 35px;
                background: #2d2d30;
                border-bottom: 1px solid #1e1e1e;
                display: flex;
                align-items: center;
            }

            /* Editor Tabs */
            .editor-tabs {
                display: flex;
                height: 100%;
                overflow-x: auto;
                scrollbar-width: thin;
            }

            /* Monaco Container */
            .monaco-container-inner {
                flex: 1;
                position: relative;
                overflow: hidden;
            }

            /* Editor Statusbar */
            .editor-statusbar {
                height: 22px;
                background: #007acc;
                color: white;
                display: flex;
                align-items: center;
                padding: 0 10px;
                font-size: 12px;
                gap: 15px;
            }

            .status-item {
                display: flex;
                align-items: center;
            }

            /* Chat Placeholder */
            .chat-placeholder {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #666;
                font-size: 14px;
            }
            
            .explorer-header {
                background: #2d2d30;
                padding: 10px 15px;
                border-bottom: 1px solid #333;
                color: #cccccc;
                font-size: 11px;
                font-weight: 600;
                letter-spacing: 0.5px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .explorer-close {
                background: none;
                border: none;
                color: #cccccc;
                cursor: pointer;
                font-size: 16px;
                padding: 2px 6px;
                border-radius: 2px;
            }
            
            .explorer-close:hover {
                background: #333;
                color: #ffffff;
            }
            
            .explorer-content {
                flex: 1;
                padding: 10px;
                overflow-y: auto;
            }

            /* Z-index Hierarchy */
            .column-divider { z-index: 10; }
            .main-workspace { z-index: 1; }
            .monaco-container-inner { z-index: 5; }
            
            /* Editor Tabs */
            .editor-tab {
                display: flex;
                align-items: center;
                padding: 0 12px;
                height: 35px;
                background: #2d2d30;
                border-right: 1px solid #252526;
                cursor: pointer;
                min-width: 120px;
                max-width: 200px;
                position: relative;
                user-select: none;
            }

            .editor-tab:hover {
                background: #353535;
            }

            .editor-tab.active {
                background: #1e1e1e;
                border-top: 2px solid #007acc;
            }

            .tab-icon {
                margin-right: 5px;
                font-size: 14px;
            }

            .tab-title {
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-size: 13px;
                color: #cccccc;
            }

            .tab-modified {
                color: #cccccc;
                margin: 0 5px;
                font-size: 16px;
            }

            .tab-close {
                background: none;
                border: none;
                color: #cccccc;
                cursor: pointer;
                padding: 2px 4px;
                margin-left: 5px;
                border-radius: 3px;
                font-size: 16px;
                line-height: 1;
                opacity: 0;
                transition: opacity 0.2s;
            }

            .editor-tab:hover .tab-close {
                opacity: 0.7;
            }

            .tab-close:hover {
                background: #464647;
                opacity: 1 !important;
            }

            /* Explorer Integration */
            .file-item {
                padding: 4px 8px;
                cursor: pointer;
                display: flex;
                align-items: center;
                font-size: 13px;
                color: #cccccc;
                border: 1px solid transparent;
                position: relative;
            }

            .file-item:hover {
                background: #2a2d2e;
            }

            .file-item.active-file {
                background: #094771;
                color: white;
            }

            .file-item.active-file::before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                width: 3px;
                background: #007acc;
            }

            .file-item[data-open="true"] {
                font-weight: 600;
            }
            
            /* Breadcrumbs */
            .editor-breadcrumbs {
                height: 22px;
                background: #252526;
                border-bottom: 1px solid #1e1e1e;
                padding: 0 10px;
                display: flex;
                align-items: center;
                font-size: 12px;
                overflow-x: auto;
                scrollbar-width: thin;
            }

            .breadcrumb-item {
                color: #969696;
                cursor: pointer;
                padding: 2px 4px;
                border-radius: 3px;
                transition: all 0.2s;
            }

            .breadcrumb-item:hover:not(.current) {
                color: #e1e1e1;
                background: #3e3e42;
            }

            .breadcrumb-item.current {
                color: #e1e1e1;
                font-weight: 600;
            }

            .breadcrumb-separator {
                color: #6a6a6a;
                margin: 0 4px;
            }

            /* Editor Actions */
            .editor-actions {
                display: flex;
                gap: 5px;
                margin-left: auto;
                margin-right: 10px;
            }

            .action-btn {
                background: transparent;
                border: 1px solid transparent;
                color: #cccccc;
                padding: 4px 8px;
                cursor: pointer;
                border-radius: 3px;
                font-size: 14px;
                transition: all 0.2s;
            }

            .action-btn:hover {
                background: #3e3e42;
                border-color: #464647;
            }

            /* Save Indicator */
            .save-indicator {
                font-weight: 600;
                min-width: 100px;
            }

            .save-indicator.saving {
                color: #f0ad4e;
                animation: pulse 1s infinite;
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.6; }
            }

            /* Notifications */
            .editor-notification {
                position: absolute;
                top: 60px;
                right: 20px;
                background: #1e1e1e;
                border: 1px solid #464647;
                padding: 10px 20px;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                z-index: 1000;
                animation: slideIn 0.3s ease-out;
            }

            .editor-notification.info {
                border-left: 4px solid #007acc;
            }

            .editor-notification.error {
                border-left: 4px solid #f44747;
            }

            .editor-notification.fade-out {
                animation: fadeOut 0.3s ease-out forwards;
            }

            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes fadeOut {
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }

            /* Auto-save status */
            .auto-save-toggle {
                position: absolute;
                bottom: 22px;
                right: 10px;
                font-size: 11px;
                color: #969696;
                display: flex;
                align-items: center;
                gap: 5px;
            }

            .auto-save-toggle input {
                cursor: pointer;
            }

            /* SPRINT 4 - UX CSS */
            .quick-open-dialog {
                position: fixed;
                top: 50px;
                left: 50%;
                transform: translateX(-50%);
                width: 500px;
                background: #2d2d30;
                border: 1px solid #464647;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                display: none;
            }

            .quick-open-input {
                width: 100%;
                padding: 10px;
                background: #3c3c3c;
                border: none;
                color: #cccccc;
                outline: none;
                font-family: 'Inter', sans-serif;
            }

            .quick-open-results {
                max-height: 300px;
                overflow-y: auto;
                border-top: 1px solid #464647;
            }

            .quick-open-item {
                padding: 8px 12px;
                cursor: pointer;
                border-bottom: 1px solid #333;
                color: #cccccc;
                font-size: 13px;
            }

            .quick-open-item:hover {
                background: #094771;
            }

            .fullscreen-mode {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                z-index: 9999 !important;
                background: #1e1e1e !important;
            }

            .fullscreen-mode #vscode-editor {
                width: 100% !important;
                height: 100% !important;
            }

            .resize-divider {
                position: absolute;
                width: 4px;
                height: 100%;
                background: #333;
                cursor: col-resize;
                z-index: 1000;
                opacity: 0;
                transition: opacity 0.2s;
            }

            .resize-divider:hover {
                opacity: 1 !important;
            }

            /* SPRINT 6 - VISUAL POLISH (H.1→H.5) */
            
            /* H.1 - Smooth transitions */
            * {
                transition: background-color 0.2s ease,
                            border-color 0.2s ease,
                            opacity 0.2s ease;
            }

            .editor-tab {
                transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .tab-opening {
                animation: tabSlideIn 0.3s ease-out;
            }

            @keyframes tabSlideIn {
                from {
                    transform: translateY(-10px);
                    opacity: 0;
                }
                to {
                    transform: translateY(0);
                    opacity: 1;
                }
            }

            /* H.2 - Loading states */
            .monaco-loader {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(30, 30, 30, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
                backdrop-filter: blur(4px);
            }

            .loader-content {
                text-align: center;
            }

            .loader-spinner {
                width: 40px;
                height: 40px;
                border: 3px solid #3c3c3c;
                border-top: 3px solid #007acc;
                border-radius: 50%;
                margin: 0 auto 15px;
                animation: spin 0.8s linear infinite;
            }

            .loader-text {
                color: #cccccc;
                font-size: 14px;
            }

            /* H.3 - Error states */
            .monaco-error-state {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: #252526;
                border: 1px solid #f44747;
                border-radius: 8px;
                padding: 30px;
                text-align: center;
                max-width: 400px;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
                z-index: 1001;
            }

            .error-icon {
                font-size: 48px;
                margin-bottom: 15px;
            }

            .error-title {
                font-size: 18px;
                font-weight: 600;
                color: #f44747;
                margin-bottom: 10px;
            }

            .error-message {
                color: #cccccc;
                margin-bottom: 10px;
            }

            .error-context {
                color: #969696;
                font-size: 12px;
                margin-bottom: 20px;
            }

            .error-actions {
                display: flex;
                gap: 10px;
                justify-content: center;
            }

            .error-btn {
                background: #007acc;
                border: none;
                color: white;
                padding: 8px 20px;
                border-radius: 4px;
                cursor: pointer;
                transition: all 0.2s;
            }

            .error-btn:hover {
                background: #005a9e;
            }

            .error-btn.secondary {
                background: #3c3c3c;
            }

            .error-btn.secondary:hover {
                background: #484848;
            }

            /* H.4 - Enhanced status bar */
            .editor-statusbar {
                display: flex;
                align-items: center;
                gap: 0;
                padding: 0 10px;
                user-select: none;
            }

            .status-item {
                display: flex;
                align-items: center;
                padding: 0 10px;
                height: 22px;
                border-right: 1px solid rgba(255, 255, 255, 0.1);
                cursor: default;
            }

            .status-item:last-child {
                border-right: none;
            }

            .status-item.clickable,
            .language-selector,
            .encoding-selector {
                cursor: pointer;
            }

            .status-item:hover.clickable,
            .language-selector:hover,
            .encoding-selector:hover {
                background: rgba(255, 255, 255, 0.1);
            }

            .status-icon {
                margin-right: 5px;
                font-size: 12px;
            }

            .status-spacer {
                flex: 1;
            }

            /* H.5 - Activity indicators */
            .activity-spinner {
                display: inline-block;
                animation: pulse 1.5s ease-in-out infinite;
            }

            .activity-spinner.loading {
                animation: spin 1s linear infinite;
            }

            .activity-spinner.saving {
                animation: pulse 0.8s ease-in-out infinite;
            }

            .activity-spinner.syncing {
                animation: spin 2s linear infinite;
            }

            @keyframes pulse {
                0%, 100% { opacity: 0.3; transform: scale(0.8); }
                50% { opacity: 1; transform: scale(1); }
            }

            /* Save flash effect */
            .save-flash {
                animation: saveFlash 0.2s ease-out;
            }

            @keyframes saveFlash {
                0% { box-shadow: inset 0 0 0 2px transparent; }
                50% { box-shadow: inset 0 0 0 2px #4ec9b0; }
                100% { box-shadow: inset 0 0 0 2px transparent; }
            }

            /* Language selector dialog */
            .language-selector-dialog {
                background: #252526;
                border: 1px solid #464647;
                border-radius: 4px;
                width: 300px;
                max-height: 400px;
                display: flex;
                flex-direction: column;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
            }

            .dialog-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            }

            .dialog-header {
                padding: 12px 16px;
                border-bottom: 1px solid #464647;
                font-weight: 600;
                color: #cccccc;
            }

            .dialog-search {
                margin: 10px;
                padding: 8px 12px;
                background: #3c3c3c;
                border: 1px solid #464647;
                border-radius: 4px;
                color: #cccccc;
            }

            .dialog-search:focus {
                outline: none;
                border-color: #007acc;
            }

            .language-list {
                flex: 1;
                overflow-y: auto;
                padding: 5px;
            }

            .language-item {
                padding: 8px 12px;
                cursor: pointer;
                border-radius: 3px;
                margin: 2px 0;
            }

            .language-item:hover {
                background: #2a2d2e;
            }

            /* Fade animations */
            .fade-out {
                animation: fadeOut 0.3s ease-out forwards;
            }

            .fade-in {
                animation: fadeIn 0.3s ease-out;
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }

            /* Smooth scrollbar */
            ::-webkit-scrollbar {
                width: 10px;
                height: 10px;
            }

            ::-webkit-scrollbar-track {
                background: #1e1e1e;
            }

            ::-webkit-scrollbar-thumb {
                background: #464647;
                border-radius: 5px;
            }

            ::-webkit-scrollbar-thumb:hover {
                background: #5a5a5a;
            }

            /* File size formatting */
            .file-size {
                min-width: 60px;
                text-align: right;
            }

            /* Connection indicator animation */
            .connection-indicator .connection-status {
                display: inline-block;
                width: 8px;
                height: 8px;
                border-radius: 50%;
                margin-right: 5px;
            }

            .connection-indicator .connection-status.connected {
                animation: pulse-green 2s ease-in-out infinite;
            }

            @keyframes pulse-green {
                0%, 100% { box-shadow: 0 0 0 0 rgba(78, 201, 176, 0.4); }
                50% { box-shadow: 0 0 0 4px rgba(78, 201, 176, 0); }
            }

            /* SPRINT 5 - Backend Integration CSS */
            .connection-status {
                position: absolute;
                bottom: 2px;
                right: 200px;
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: #f44747;
                transition: all 0.3s;
            }

            .connection-status.connected {
                background: #4ec9b0;
                box-shadow: 0 0 4px #4ec9b0;
            }

            .editor-tab.deleted {
                opacity: 0.5;
                text-decoration: line-through;
            }

            .editor-tab.deleted .tab-title {
                color: #f44747;
            }

            .editor-notification {
                position: fixed;
                top: 100px;
                right: 20px;
                background: #252526;
                border: 1px solid #464647;
                border-radius: 4px;
                padding: 12px;
                max-width: 300px;
                color: #cccccc;
                font-size: 13px;
                z-index: 10000;
                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            }

            .editor-notification.warning {
                border-left: 4px solid #f0ad4e;
            }

            .editor-notification.error {
                border-left: 4px solid #f44747;
            }

            .editor-notification.info {
                border-left: 4px solid #007acc;
            }

            .notification-actions {
                display: flex;
                gap: 8px;
                margin-top: 8px;
            }

            .notification-action {
                background: #3c3c3c;
                border: 1px solid #464647;
                color: #cccccc;
                padding: 4px 12px;
                border-radius: 3px;
                cursor: pointer;
                font-size: 12px;
                transition: all 0.2s;
            }

            .notification-action:hover {
                background: #464647;
                border-color: #007acc;
            }

            .api-loading {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                display: flex;
                align-items: center;
                gap: 10px;
                background: #252526;
                padding: 20px;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            }

            .spinner {
                width: 20px;
                height: 20px;
                border: 2px solid #464647;
                border-top: 2px solid #007acc;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            /* MULTI-CHAT PANEL - Painel para conversa multi-IA */
            .multi-chat-panel {
                position: fixed;
                top: 0;
                left: 80px;
                right: 0;
                bottom: 0;
                background: #0a0a0a;
                border-left: 1px solid #333;
                z-index: 2;
                transform: translateX(100%);
                transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                overflow: hidden;
                display: flex;
                flex-direction: column;
            }

            .multi-chat-panel.active {
                transform: translateX(0);
            }

            .multi-chat-header {
                background: #171717;
                padding: 20px;
                border-bottom: 1px solid #333;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .multi-chat-content {
                flex: 1;
                display: flex;
                flex-direction: column;
                padding: 20px;
                gap: 20px;
                overflow-y: auto;
            }

            .model-selection {
                background: #171717;
                padding: 20px;
                border-radius: 12px;
                border: 1px solid #333;
            }

            .model-checkbox {
                display: flex;
                align-items: center;
                gap: 10px;
                margin-bottom: 10px;
                padding: 10px;
                background: #202020;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s;
            }

            .model-checkbox:hover {
                background: #2a2a2a;
                border: 1px solid #444;
            }

            .model-checkbox input[type="checkbox"] {
                width: 20px;
                height: 20px;
                cursor: pointer;
            }

            .conversation-display {
                background: #171717;
                padding: 20px;
                border-radius: 12px;
                border: 1px solid #333;
                flex: 1;
                overflow-y: auto;
            }

            .round-container {
                margin-bottom: 30px;
                padding: 20px;
                background: #202020;
                border-radius: 12px;
                border: 1px solid #333;
            }

            .round-header {
                font-size: 16px;
                font-weight: 600;
                color: #ff0000;
                margin-bottom: 15px;
                text-shadow: 0 0 10px rgba(255, 0, 0, 0.3);
            }

            .ai-response {
                background: #252525;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
                border-left: 3px solid #666;
            }

            .ai-response.thinking {
                opacity: 0.5;
                border-left-color: #ff0000;
                animation: pulse 1s infinite;
            }

            @keyframes pulse {
                0% { opacity: 0.5; }
                50% { opacity: 0.8; }
                100% { opacity: 0.5; }
            }

            .ai-name {
                font-weight: 600;
                color: #4CAF50;
                margin-bottom: 5px;
            }

            .multi-chat-controls {
                background: #171717;
                padding: 20px;
                border-top: 1px solid #333;
                display: flex;
                gap: 20px;
                align-items: flex-end;
            }

            .multi-chat-input {
                flex: 1;
                background: #202020;
                border: 1px solid #333;
                color: #fff;
                padding: 15px;
                border-radius: 12px;
                font-size: 14px;
                font-family: 'Inter', sans-serif;
                outline: none;
                resize: none;
                min-height: 60px;
            }

            .rounds-control {
                display: flex;
                align-items: center;
                gap: 10px;
                color: #999;
            }

            .rounds-input {
                width: 50px;
                background: #202020;
                border: 1px solid #333;
                color: #fff;
                padding: 8px;
                border-radius: 6px;
                text-align: center;
            }

            .btn-start-chat {
                background: #ff0000;
                border: none;
                color: #fff;
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s;
            }

            .btn-start-chat:hover {
                background: #ff3333;
                box-shadow: 0 0 20px rgba(255, 0, 0, 0.5);
            }

            .btn-start-chat:disabled {
                background: #444;
                cursor: not-allowed;
                opacity: 0.5;
            }

            .logs-header {
                background: #171717;
                padding: 20px;
                border-bottom: 1px solid #333;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .logs-header h2 {
                font-size: 20px;
                margin: 0;
            }

            .logs-filters {
                background: #171717;
                padding: 15px 20px;
                border-bottom: 1px solid #333;
                display: flex;
                gap: 15px;
                align-items: center;
            }

            .logs-content {
                flex: 1;
                overflow-y: auto;
                padding: 20px;
                background: #0a0a0a;
            }

            .logs-table {
                width: 100%;
                background: #171717;
                border: 1px solid #333;
                border-radius: 8px;
                overflow: hidden;
            }

            .logs-table table {
                width: 100%;
                border-collapse: collapse;
            }

            .logs-table th {
                background: #202020;
                padding: 12px;
                text-align: left;
                font-weight: 600;
                border-bottom: 2px solid #333;
                font-size: 13px;
                color: #999;
            }

            .logs-table td {
                padding: 10px 12px;
                border-bottom: 1px solid #252525;
                font-size: 12px;
            }

            .logs-table tr:hover {
                background: rgba(255, 255, 255, 0.02);
            }

            .log-level-INFO { color: #4CAF50; }
            .log-level-WARNING { color: #FFC107; }
            .log-level-ERROR { color: #f44336; }
            .log-level-DEBUG { color: #2196F3; }

            .logs-stats {
                background: #171717;
                padding: 15px 20px;
                border-top: 1px solid #333;
                display: flex;
                gap: 30px;
                font-size: 13px;
                color: #999;
            }

            .logs-close {
                background: none;
                border: none;
                color: #666;
                font-size: 24px;
                cursor: pointer;
                transition: color 0.3s;
            }

            .logs-close:hover {
                color: #ff0000;
            }

            .logs-tabs {
                display: flex;
                gap: 10px;
                margin-top: 10px;
            }

            .log-tab {
                background: #202020;
                border: 1px solid #333;
                color: #999;
                padding: 8px 16px;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s;
            }

            .log-tab.active {
                background: #ff0000;
                color: #fff;
                border-color: #ff0000;
            }

            .conversation-view {
                background: #171717;
                border: 1px solid #333;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 20px;
            }

            .conv-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
                padding-bottom: 10px;
                border-bottom: 1px solid #333;
            }

            .conv-messages {
                margin-bottom: 15px;
            }

            .user-msg, .assistant-msg {
                margin-bottom: 10px;
                padding: 10px;
                border-radius: 8px;
            }

            .user-msg {
                background: #1a1a2e;
                border-left: 3px solid #0066cc;
            }

            .assistant-msg {
                background: #1a2e1a;
                border-left: 3px solid #00cc66;
            }

            .cascade-analysis {
                margin-left: 20px;
                padding-left: 20px;
                border-left: 2px solid #333;
                margin-top: 10px;
            }

            .analysis-item {
                background: #202020;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }

            .model-badge {
                display: inline-block;
                background: #ff0000;
                color: #fff;
                padding: 4px 12px;
                border-radius: 20px;
                font-size: 11px;
                font-weight: 600;
                margin-bottom: 8px;
            }

            .btn-small {
                background: #333;
                border: 1px solid #555;
                color: #fff;
                padding: 6px 12px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 12px;
                transition: all 0.3s;
            }

            .btn-small:hover {
                background: #ff0000;
                border-color: #ff0000;
            }

            .btn-danger {
                background: #dc3545 !important;
                border-color: #dc3545 !important;
                color: #fff !important;
                cursor: pointer !important;
            }

            .btn-danger:hover {
                background: #c82333 !important;
                border-color: #bd2130 !important;
            }

            .btn {
                background: #333;
                border: 1px solid #555;
                color: #fff;
                padding: 8px 15px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 12px;
                transition: all 0.3s;
                text-decoration: none;
                display: inline-block;
            }

            .btn:hover {
                background: #555;
            }

            .conversations-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px;
                background: #171717;
                border: 1px solid #333;
                border-radius: 8px;
                margin-bottom: 15px;
            }

            .conv-controls {
                display: flex;
                gap: 10px;
            }

            .conv-info {
                color: #999;
                font-size: 14px;
            }

            /* Dropdown styles */
            .dropdown {
                position: relative;
                display: inline-block;
            }

            .dropdown-toggle {
                cursor: pointer;
            }

            .dropdown-menu {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                background: #2a2a2a;
                border: 1px solid #444;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                z-index: 1000;
                min-width: 200px;
                padding: 5px 0;
            }

            .dropdown-menu.show {
                display: block;
            }

            .dropdown-menu a {
                display: block;
                padding: 8px 15px;
                color: #fff;
                text-decoration: none;
                transition: background 0.2s;
                font-size: 13px;
            }

            .dropdown-menu a:hover {
                background: #444;
            }

            .dropdown-menu a.danger {
                color: #ff6b6b;
                font-weight: 600;
            }

            .dropdown-menu a.danger:hover {
                background: #dc3545;
                color: #fff;
            }

            .dropdown-divider {
                height: 1px;
                background: #444;
                margin: 5px 0;
            }

            /* Filtros dentro do painel */
            .log-filter {
                background: #202020;
                border: 1px solid #333;
                color: #fff;
                padding: 6px 12px;
                border-radius: 6px;
                font-size: 12px;
                outline: none;
            }

        </style>
    </head>
    <body>
        <div class="main-container">
            <!-- SIDEBAR -->
            <div id="sidebar-container">
                <!-- CAMADA SEPARADA: LOGO -->
                <div class="logo-area">
                    <div id="logo-container">
                        <video id="logo-video" autoplay loop muted>
                            <source src="/assets/logoglow.mp4" type="video/mp4">
                        </video>
                    </div>
                </div>

                <!-- CAMADA SEPARADA: BOTÕES -->
                <div class="buttons-area">
                    <div id="buttons-container">
                        <img src="/assets/01.png" class="sidebar-button btn-red" alt="Button 1" data-btn="1">
                        <img src="/assets/02.png" class="sidebar-button btn-white" alt="Button 2" data-btn="2">
                        <img src="/assets/03.png" class="sidebar-button btn-white" alt="Button 3" data-btn="3">
                        <img src="/assets/04.png" class="sidebar-button btn-white" alt="Button 4" data-btn="4">
                        <img src="/assets/05.png" class="sidebar-button btn-white" alt="Button 5" data-btn="5">
                        <img src="/assets/06.png" class="sidebar-button btn-red" alt="Button 6" data-btn="6">
                        <img src="/assets/07.png" class="sidebar-button btn-red" alt="Button 7" data-btn="7">
                        <img src="/assets/08.png" class="sidebar-button btn-red" alt="Button 8" data-btn="8">
                    </div>
                </div>
            </div>
            
            <!-- CHAT CONTENT -->
            <div class="chat-content">
                <!-- SELETOR DE MODELO -->
                <select id="modelSelect" class="model-selector">
                    <option value="llama2">llama2</option>
                </select>

                <!-- ÁREA DE MENSAGENS -->
                <div class="messages-area" id="messagesArea">
                    <div class="messages-inner" id="messagesInner"></div>
                </div>

                <!-- INPUT ÁREA -->
                <div class="input-area">
                    <div class="input-inner">
                        <textarea
                            id="messageInput"
                            class="message-input"
                            onkeydown="handleKeyPress(event)"
                            oninput="autoResize(this)"
                            placeholder="..."
                        ></textarea>
                    </div>
                </div>
            </div>
        </div>

        <script>
            
            // ELEMENTOS DOM
            const messagesArea = document.getElementById('messagesArea');
            const messagesInner = document.getElementById('messagesInner');
            const messageInput = document.getElementById('messageInput');
            const modelSelect = document.getElementById('modelSelect');
            
            // VARIÁVEL GLOBAL PARA O INDICADOR
            let typingIndicator = null;

            // CARREGA MODELOS DISPONÍVEIS
            async function loadModels() {
                try {
                    console.log('Carregando modelos...');
                    const response = await fetch('/models');
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    const models = await response.json();
                    console.log('Modelos carregados:', models);

                    if (!modelSelect) {
                        console.error('❌ modelSelect não encontrado!');
                        return;
                    }

                    modelSelect.innerHTML = '';
                    
                    // Agrupar modelos por tipo
                    const localModels = models.filter(m => m.type === 'local');
                    const cloudModels = models.filter(m => m.type === 'cloud');
                    
                    // Adicionar modelos locais
                    if (localModels.length > 0) {
                        const localGroup = document.createElement('optgroup');
                        localGroup.label = '🖥️ Modelos Locais (Ollama)';
                        localModels.forEach(model => {
                            const option = document.createElement('option');
                            option.value = model.name;
                            option.textContent = model.name;
                            localGroup.appendChild(option);
                        });
                        modelSelect.appendChild(localGroup);
                    }
                    
                    // Adicionar modelos externos
                    if (cloudModels.length > 0) {
                        const cloudGroup = document.createElement('optgroup');
                        cloudGroup.label = '☁️ APIs Externas (GPT-4, Claude, etc.)';
                        cloudModels.forEach(model => {
                            const option = document.createElement('option');
                            option.value = model.name;
                            // Adicionar emoji por provedor
                            let emoji = '🤖';
                            if (model.name.includes('gpt')) emoji = '🟢';
                            else if (model.name.includes('claude')) emoji = '🟠';
                            else if (model.name.includes('gemini')) emoji = '🔵';
                            else if (model.name.includes('deepseek')) emoji = '🟣';
                            
                            option.textContent = `${emoji} ${model.name}`;
                            cloudGroup.appendChild(option);
                        });
                        modelSelect.appendChild(cloudGroup);
                    }

                    // Selecionar llama3:8b como padrão se disponível
                    const defaultModel = 'llama3:8b';
                    const defaultOption = modelSelect.querySelector(`option[value="${defaultModel}"]`);
                    if (defaultOption) {
                        modelSelect.value = defaultModel;
                        console.log(`🎯 Modelo padrão selecionado: ${defaultModel}`);
                    } else {
                        // Se llama3:8b não estiver disponível, selecionar o primeiro modelo local
                        if (localModels.length > 0) {
                            modelSelect.value = localModels[0].name;
                            console.log(`🎯 Modelo padrão alternativo: ${localModels[0].name}`);
                        }
                    }

                    console.log(`Modelos carregados: ${localModels.length} locais + ${cloudModels.length} externos`);
                } catch (error) {
                    console.error('Erro ao carregar modelos:', error);
                    
                    // Fallback para modelos padrão se falhar
                    if (modelSelect) {
                        modelSelect.innerHTML = `
                            <optgroup label="Modelos Locais (Ollama)">
                                <option value="llama3:8b">llama3:8b</option>
                                <option value="dolphin-llama3:8b">dolphin-llama3:8b</option>
                                <option value="lilith-uncensored:latest">lilith-uncensored:latest</option>
                            </optgroup>
                        `;
                        modelSelect.value = 'llama3:8b';
                        console.log('Usando modelos padrão como fallback');
                    }
                }
            }

            // 🎭 APELIDOS DAS IAS
            const aiNicknames = {
                'dolphin': 'Dolfunny',
                'dolphin-llama3': 'Golfinho',
                'dolphin-mixtral': 'Golfinho',
                'lilith': 'Lilith',
                'llama3': 'Llama',
                'llama2': 'Llama',
                'llama': 'Llama',
                'mistral': 'Queen',
                'mixtral': 'Queen',
                'qwen': 'Queen',
                'gpt-4': 'GPT4',
                'gpt4': 'GPT4',
                'gpt-4o': 'GPT4o',
                'gpt4o': 'GPT4o',
                'claude': 'Claudete',
                'claude-sonnet': 'Claudete',
                'claude-3': 'Claudete',
                'gemini': 'Gemininha',
                'gemini-pro': 'Gemininha',
                'deepseek': 'DeepSeek',
                'deepseek-v3': 'DeepSeek'
            };

            function getAiNickname(modelName) {
                // Busca exata primeiro
                if (aiNicknames[modelName]) {
                    return aiNicknames[modelName];
                }
                
                // Busca parcial
                for (const [key, nickname] of Object.entries(aiNicknames)) {
                    if (modelName.toLowerCase().includes(key.toLowerCase())) {
                        return nickname;
                    }
                }
                
                return 'IA'; // fallback
            }

            // 🚀 FUNÇÃO COM TYPEWRITER EFFECT + APELIDOS
            function addMessage(sender, message, modelName = '') {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;
                
                console.log('🔍 addMessage - sender:', sender, 'message:', message, 'model:', modelName);
                
                // 🎨 DETECTA COMANDOS DE IMAGEM
                if (message.startsWith('[IMAGE]')) {
                    console.log('🖼️ Renderizando imagem gerada');
                    const imageData = message.replace('[IMAGE]', '');
                    
                    const img = document.createElement('img');
                    img.src = `data:image/png;base64,${imageData}`;
                    img.style.maxWidth = '100%';
                    img.style.borderRadius = '12px';
                    img.style.marginTop = '10px';
                    
                    const caption = document.createElement('div');
                    caption.textContent = 'A Imaginação é o primeiro plano da criação';
                    caption.style.fontSize = '12px';
                    caption.style.color = '#888';
                    caption.style.marginTop = '5px';
                    
                    messageDiv.appendChild(img);
                    messageDiv.appendChild(caption);
                    messagesInner.appendChild(messageDiv);
                } else {
                    // 🎭 ADICIONA APELIDO PARA ASSISTANT
                    if (sender === 'assistant' && modelName) {
                        const nickname = getAiNickname(modelName);
                        const finalMessage = `${nickname}: ${message}`;
                        console.log('⌨️ TYPEWRITER EFFECT ATIVADO com apelido:', nickname);
                        messageDiv.textContent = '';
                        messagesInner.appendChild(messageDiv);
                        typewriterEffect(messageDiv, finalMessage);
                    } else if (sender === 'assistant') {
                        console.log('⌨️ TYPEWRITER EFFECT ATIVADO sem apelido');
                        messageDiv.textContent = '';
                        messagesInner.appendChild(messageDiv);
                        typewriterEffect(messageDiv, message);
                    } else {
                        console.log('💀 USER MESSAGE - SEM TYPEWRITER');
                        // Aplicar markdown em mensagens do usuário
                        try {
                            messageDiv.innerHTML = formatMarkdown(message);
                        } catch (error) {
                            console.log('❌ Erro no markdown, usando texto normal:', error);
                            messageDiv.innerHTML = message;
                        }
                        messagesInner.appendChild(messageDiv);
                    }
                }

                messagesArea.scrollTop = messagesArea.scrollHeight;
            }

            // ⌨️ EFEITO TYPEWRITER
            function typewriterEffect(element, text, speed = 15) {
                // Se o texto contém HTML, usar innerHTML diretamente
                if (text.includes('<') && text.includes('>')) {
                    element.innerHTML = text;
                    messagesArea.scrollTop = messagesArea.scrollHeight;
                    return;
                }
                
                // Caso contrário, usar o typewriter tradicional
                let i = 0;
                element.textContent = '';
                
                function type() {
                    if (i < text.length) {
                        element.textContent += text.charAt(i);
                        i++;
                        messagesArea.scrollTop = messagesArea.scrollHeight;
                        setTimeout(type, speed);
                    }
                }
                
                type();
            }
            


            // MOSTRA INDICADOR DE DIGITAÇÃO
            function showTypingIndicator() {
                // Remove indicador anterior se existir
                if (typingIndicator) {
                    hideTypingIndicator();
                }

                // Cria novo indicador
                typingIndicator = document.createElement('div');
                typingIndicator.className = 'typing-indicator';
                typingIndicator.innerHTML = '<div class="typing-dot"></div>';

                // Adiciona onde a mensagem da IA vai aparecer
                messagesInner.appendChild(typingIndicator);

                // Scroll automático
                messagesArea.scrollTop = messagesArea.scrollHeight;

                console.log('🔥 Indicador de digitação ativado');
            }

            // ESCONDE INDICADOR DE DIGITAÇÃO
            function hideTypingIndicator() {
                if (typingIndicator) {
                    typingIndicator.remove();
                    typingIndicator = null;
                    console.log('❄️  Indicador de digitação removido');
                }
            }

            // ✅ ENTER LIMPO - REGRA DE OURO
            function handleKeyPress(event) {
                if (event.key === 'Enter' && !event.shiftKey) {
                    event.preventDefault();
                    sendMessage();
                } else if (event.key === 'Enter' && event.shiftKey) {
                    // Permite quebra de linha com Shift+Enter
                }
            }

            // AUTO RESIZE DO TEXTAREA
            function autoResize(textarea) {
                textarea.style.height = '100px';
                const newHeight = Math.min(Math.max(textarea.scrollHeight, 100), 400);
                textarea.style.height = newHeight + 'px';
            }

            // 🎨 INPUT COM COMANDOS DE IMAGEM MÁGICOS!
            async function sendMessage() {
                console.log('🔍 messageInput element:', messageInput);
                console.log('🔍 messageInput.value RAW:', messageInput.value);
                console.log('🔍 messageInput.value length:', messageInput.value.length);
                
                const rawText = messageInput.value.trim();
                console.log('🔍 rawText after trim:', rawText);
                if (!rawText) {
                    console.log('❌ rawText is empty, returning');
                    return;
                }

                // 🎨 DETECTA COMANDOS DE IMAGEM
                if (rawText.startsWith('/realistic ') || rawText.startsWith('/ultra ')) {
                    console.log('🎨 Comando de imagem detectado!');
                    
                    const isUltra = rawText.startsWith('/ultra ');
                    const prompt = rawText.replace('/realistic ', '').replace('/ultra ', '');
                    
                    addMessage('user', rawText);
                    messageInput.value = '';
                    messageInput.style.height = '100px';
                    
                    showTypingIndicator();
                    
                    // Prompt especial para sua foto
                    let fullPrompt = prompt;
                    if (prompt.toLowerCase().includes('claude') || prompt.toLowerCase().includes('minha foto')) {
                        fullPrompt = "Professional portrait of a beautiful tech developer woman, long wavy black-blue hair with screen reflection highlights, deep violet eyes with golden flecks, elegant cyberpunk style, black leather jacket over band t-shirt, black cargo pants, combat boots, binary code tattoo on forearm, industrial ear piercing, black holographic nails, mysterious confident smile, photorealistic, highly detailed, 4k";
                    }
                    
                    try {
                        const imageResponse = await fetch('/generate-image', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                prompt: fullPrompt,
                                negative_prompt: "low quality, blurry, distorted, deformed",
                                width: isUltra ? 1024 : 768,
                                height: isUltra ? 1024 : 768,
                                steps: isUltra ? 50 : 40,
                                cfg_scale: 7.0
                            })
                        });
                        
                        const imageData = await imageResponse.json();
                        hideTypingIndicator();
                        
                        if (imageData.success && imageData.images[0]) {
                            addMessage('assistant', `[IMAGE]${imageData.images[0]}`);
                        } else {
                            addMessage('assistant', `❌ Erro na geração: ${imageData.error}`);
                        }
                    } catch (error) {
                        hideTypingIndicator();
                        addMessage('assistant', `❌ Erro de conexão SD: ${error.message}`);
                    }
                    return;
                }

                // CHAT NORMAL
                addMessage('user', rawText);
                messageInput.value = '';
                messageInput.style.height = '100px';
                showTypingIndicator();

                try {
                    const response = await fetch('/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            message: rawText,
                            model: modelSelect.value
                        })
                    });

                    const data = await response.json();
                    hideTypingIndicator();

                    if (response.ok) {
                        addMessage('assistant', data.response, modelSelect.value);
                    } else {
                        addMessage('assistant', `❌ Erro: ${data.detail}`, modelSelect.value);
                    }
                } catch (error) {
                    hideTypingIndicator();
                    addMessage('assistant', `❌ Erro de conexão: ${error.message}`);
                }
            }

            // LIMPA CHAT
            function clearChat() {
                messagesInner.innerHTML = '';
                hideTypingIndicator();
            }

            // LOGS PANEL FUNCTIONS
            let logsRefreshInterval;
            let logsPanelOpen = false;

            function toggleLogsPanel() {
                const panel = document.getElementById('logsPanel');
                logsPanelOpen = !logsPanelOpen;

                if (logsPanelOpen) {
                    panel.classList.add('active');
                    try {
                        updateLogs();
                        startLogsAutoRefresh();
                    } catch(e) {}
                } else {
                    panel.classList.remove('active');
                    stopLogsAutoRefresh();
                }
            }

            // VSCode Monaco Panel Control
            let monacoEditor = null;
            let monacoPanelOpen = false;

            function initializeMonaco() {
                if (window.monacoWorkspace) return; // Já inicializado

                console.log('🖥️ Inicializando Monaco Editor...');

                // Inicializar a classe MonacoWorkspace
                window.monacoWorkspace = new MonacoWorkspace();
            }

            function toggleMonacoPanel() {
                const panel = document.getElementById('monacoPanel');
                monacoPanelOpen = !monacoPanelOpen;

                if (monacoPanelOpen) {
                    panel.classList.add('active');
                    try {
                        initializeMonaco();
                    } catch(e) {}
                } else {
                    panel.classList.remove('active');
                }
            }

            // 🔬 DIAGNÓSTICO COMPLETO - VAMOS VER O QUE ESTÁ ACONTECENDO
            document.addEventListener('DOMContentLoaded', function() {
                console.log('DOM carregado');

                // TESTE DIRETO - Verificar se os botões respondem
                const btn2 = document.querySelector('[data-btn="2"]');
                const btn8 = document.querySelector('[data-btn="8"]');

                if (btn2) {
                    console.log('Botao 2 encontrado, anexando listener');
                    btn2.onclick = function() {
                        console.log('BOTAO 2 CLICADO!');
                        toggleMonacoPanel();
                    };
                }

                if (btn8) {
                    console.log('Botao 8 encontrado, anexando listener');
                    btn8.onclick = function() {
                        console.log('BOTAO 8 CLICADO!');
                        toggleLogsPanel();
                    };
                }


                // Verificar se as funções estão definidas
                console.log('🔍 toggleMonacoPanel:', typeof toggleMonacoPanel);
                console.log('🔍 toggleLogsPanel:', typeof toggleLogsPanel);

                // Verificar se os painéis existem no DOM
                const monacoPanel = document.getElementById('monacoPanel');
                const logsPanel = document.getElementById('logsPanel');
                console.log('🔍 Monaco Panel no DOM:', monacoPanel);
                console.log('🔍 Logs Panel no DOM:', logsPanel);

                if (monacoPanel) {
                    console.log('🔍 Monaco Panel computed style:', window.getComputedStyle(monacoPanel).transform);
                }
                if (logsPanel) {
                    console.log('🔍 Logs Panel computed style:', window.getComputedStyle(logsPanel).transform);
                }

                // TESTE DIRETO - Adicionar listeners de teste
                console.log('🧪 TESTE DIRETO DOS BOTÕES');
                const allButtons = document.querySelectorAll('.sidebar-button');
                console.log('🔍 Total de botões encontrados:', allButtons.length);

                allButtons.forEach((btn, index) => {
                    console.log(`🔍 Botão ${index + 1}:`, btn, 'data-btn:', btn.getAttribute('data-btn'));
                    btn.addEventListener('click', function() {
                        console.log(`🎯 CLIQUE DETECTADO no botão ${index + 1} (data-btn: ${btn.getAttribute('data-btn')})`);
                    });
                });

                // Carrega modelos
                loadModels();
                
                // SPRINT 4 - Inicializar managers UX
                if (typeof ResizeManager !== 'undefined') {
                    window.resizeManager = new ResizeManager();
                    console.log('🎯 ResizeManager inicializado');
                }
                
                if (typeof ShortcutManager !== 'undefined' && typeof monacoWorkspace !== 'undefined') {
                    window.shortcutManager = new ShortcutManager(monacoWorkspace);
                    console.log('⌨️ ShortcutManager inicializado');
                }
                
                
                console.log('✅ Diagnóstico completo');
            });

            // MULTI-CHAT PANEL FUNCTIONS
            let multiChatPanelOpen = false;
            let multiChatConversation = [];
            let selectedModels = [];
            
            function toggleMultiChatPanel() {
                const panel = document.getElementById('multiChatPanel');
                multiChatPanelOpen = !multiChatPanelOpen;
                
                if (multiChatPanelOpen) {
                    panel.classList.add('active');
                    loadAvailableModels();
                } else {
                    panel.classList.remove('active');
                }
            }
            
            async function loadAvailableModels() {
                try {
                    const response = await fetch('/models');
                    const models = await response.json();
                    
                    const container = document.getElementById('modelSelectionContainer');
                    container.innerHTML = '<h3>Selecione os modelos para conversar:</h3>';
                    
                    models.forEach(model => {
                        const div = document.createElement('div');
                        div.className = 'model-checkbox';
                        div.innerHTML = `
                            <input type="checkbox" id="model-${model}" value="${model}" onchange="updateSelectedModels()">
                            <label for="model-${model}">${model}</label>
                        `;
                        container.appendChild(div);
                    });
                } catch (error) {
                    console.error('Erro ao carregar modelos:', error);
                }
            }
            
            function updateSelectedModels() {
                selectedModels = [];
                const checkboxes = document.querySelectorAll('#modelSelectionContainer input[type="checkbox"]:checked');
                checkboxes.forEach(cb => selectedModels.push(cb.value));
                
                const startBtn = document.getElementById('startMultiChatBtn');
                startBtn.disabled = selectedModels.length < 2;
            }
            
            async function startMultiChat() {
                const message = document.getElementById('multiChatInput').value.trim();
                const rounds = parseInt(document.getElementById('roundsInput').value) || 3;
                
                if (!message) {
                    alert('Digite uma mensagem para iniciar a conversa');
                    return;
                }
                
                if (selectedModels.length < 2) {
                    alert('Selecione pelo menos 2 modelos');
                    return;
                }
                
                // Limpar display
                const display = document.getElementById('conversationDisplay');
                display.innerHTML = '<div style="text-align: center; color: #666;">Iniciando conversa...</div>';
                
                // Desabilitar controles
                document.getElementById('startMultiChatBtn').disabled = true;
                document.getElementById('multiChatInput').disabled = true;
                
                try {
                    const response = await fetch('/api/multi-chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            message: message,
                            models: selectedModels,
                            rounds: rounds
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok) {
                        multiChatConversation = data.conversation;
                        displayMultiChatConversation();
                    } else {
                        display.innerHTML = `<div style="color: #ff0000;">Erro: ${data.detail}</div>`;
                    }
                } catch (error) {
                    display.innerHTML = `<div style="color: #ff0000;">Erro de conexão: ${error.message}</div>`;
                } finally {
                    // Reabilitar controles
                    document.getElementById('startMultiChatBtn').disabled = false;
                    document.getElementById('multiChatInput').disabled = false;
                }
            }
            
            function displayMultiChatConversation() {
                const display = document.getElementById('conversationDisplay');
                display.innerHTML = '';
                
                multiChatConversation.forEach(round => {
                    const roundDiv = document.createElement('div');
                    roundDiv.className = 'round-container';
                    
                    const header = document.createElement('div');
                    header.className = 'round-header';
                    header.textContent = `🔄 Rodada ${round.round}`;
                    roundDiv.appendChild(header);
                    
                    round.responses.forEach(response => {
                        const respDiv = document.createElement('div');
                        respDiv.className = 'ai-response';
                        
                        const nameDiv = document.createElement('div');
                        nameDiv.className = 'ai-name';
                        nameDiv.textContent = `🤖 ${response.model}`;
                        
                        const textDiv = document.createElement('div');
                        textDiv.textContent = response.response;
                        
                        respDiv.appendChild(nameDiv);
                        respDiv.appendChild(textDiv);
                        roundDiv.appendChild(respDiv);
                    });
                    
                    display.appendChild(roundDiv);
                });
                
                // Scroll para o final
                display.scrollTop = display.scrollHeight;
            }



            // Inicialização do Monaco Editor
            class MonacoWorkspace {
                constructor() {
                    this.editor = null;
                    this.currentFile = null;
                    this.openFiles = new Map(); // path -> {content, model, viewState}
                    this.activeTab = null;
                    this.isDirty = new Map(); // arquivo -> boolean
                    this.autoSaveTimer = null;
                    this.autoSaveEnabled = true;
                    this.autoSaveDelay = 2000; // 2 segundos
                    
                    this.initializeMonaco();
                    this.setupExplorerIntegration();
                }
                
                initializeMonaco() {
                    // Configurar o loader do Monaco
                    require.config({ 
                        paths: { 
                            'vs': 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs' 
                        }
                    });
                    
                    // Carregar o Monaco
                    require(['vs/editor/editor.main'], () => {
                        this.createEditor();
                    });
                }
                
                createEditor() {
                    // Opções do editor
                    const editorOptions = {
                        value: '# Bem-vindo ao Monaco Editor\n# Pressione Ctrl+S para salvar\n\nprint("Hello, Lilith!")',
                        language: 'python',
                        theme: 'vs-dark',
                        fontSize: 14,
                        fontFamily: 'Consolas, "Courier New", monospace',
                        automaticLayout: true,
                        minimap: {
                            enabled: true,
                            side: 'right'
                        },
                        scrollBeyondLastLine: false,
                        wordWrap: 'on',
                        lineNumbers: 'on',
                        renderWhitespace: 'selection',
                        contextmenu: true,
                        folding: true,
                        bracketPairColorization: {
                            enabled: true
                        },
                        suggest: {
                            showKeywords: true,
                            showSnippets: true
                        }
                    };
                    
                    // Criar o editor
                    const container = document.getElementById('monacoContainer');
                    this.editor = monaco.editor.create(container, editorOptions);
                    
                    // Setup inicial
                    this.setupEventListeners();
                    this.setupThemes();
                    this.setupBreadcrumbs();
                    this.setupAdvancedSyntax();
                    this.setupAutoSave();
                    this.setupUndoRedo();
                    this.setupWebSocket();
                    this.setupStatusBar();
                    
                    // Inicializar AI Integration
                    this.aiIntegration = new AIIntegration(this);
                    
                    // Expor API global para o chat
                    window.monacoAI = this.aiIntegration;
                    
                    console.log('✅ Monaco Editor inicializado');
                }
                
                setupEventListeners() {
                    // Mudanças no editor
                    this.editor.onDidChangeModelContent(() => {
                        this.updateStatusBar();
                    });
                    
                    // Posição do cursor
                    this.editor.onDidChangeCursorPosition((e) => {
                        this.updateCursorPosition(e.position);
                    });
                    
                    // Atalhos de teclado
                    this.editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
                        this.saveCurrentFile();
                    });
                }
                
                setupThemes() {
                    // Definir tema VS Dark customizado
                    monaco.editor.defineTheme('lilith-dark', {
                        base: 'vs-dark',
                        inherit: true,
                        rules: [
                            { token: 'comment', foreground: '608B4E' },
                            { token: 'keyword', foreground: 'C586C0' },
                            { token: 'string', foreground: 'CE9178' }
                        ],
                        colors: {
                            'editor.background': '#1e1e1e',
                            'editor.foreground': '#d4d4d4',
                            'editor.lineHighlightBackground': '#2d2d30',
                            'editorCursor.foreground': '#AEAFAD',
                            'editor.selectionBackground': '#264f78'
                        }
                    });
                    
                    // Aplicar tema
                    monaco.editor.setTheme('lilith-dark');
                }
                
                updateStatusBar() {
                    const model = this.editor.getModel();
                    const lineCount = model.getLineCount();
                    const language = model.getLanguageId();
                    
                    // Atualizar status (implementar depois)
                    console.log(`Lines: ${lineCount}, Language: ${language}`);
                }
                
                updateCursorPosition(position) {
                    const statusBar = document.querySelector('.editor-statusbar');
                    const positionItem = statusBar.querySelector('.status-item:last-child');
                    positionItem.textContent = `Ln ${position.lineNumber}, Col ${position.column}`;
                }
                
                
                // D.1 - Click arquivo → abre no Monaco
                setupExplorerIntegration() {
                    // Observar clicks no explorer
                    document.addEventListener('click', (e) => {
                        const fileItem = e.target.closest('.file-item');
                        if (fileItem && fileItem.dataset.path) {
                            e.preventDefault();
                            this.openFile(fileItem.dataset.path);
                        }
                    });
                }
                
                // D.2 - Sistema de arquivo ativo
                async openFile(filePath) {
                    console.log(`📂 Abrindo arquivo: ${filePath}`);
                    
                    // Mostrar loading
                    this.showActivity('loading');
                    
                    // Verificar se já está aberto
                    if (this.openFiles.has(filePath)) {
                        this.switchToFile(filePath);
                        this.hideActivity();
                        return;
                    }
                    
                    try {
                        // Buscar conteúdo do arquivo
                        const response = await fetch('/api/read-file', {
                            method: 'POST',
                            headers: {'Content-Type': 'application/json'},
                            body: JSON.stringify({path: filePath})
                        });
                        
                        if (!response.ok) throw new Error('Erro ao ler arquivo');
                        
                        const data = await response.json();
                        const content = data.content || '';
                        const language = this.detectLanguage(filePath);
                        
                        // Criar modelo do Monaco
                        const model = monaco.editor.createModel(content, language);
                        
                        // Armazenar arquivo aberto
                        this.openFiles.set(filePath, {
                            path: filePath,
                            model: model,
                            viewState: null,
                            modified: false
                        });
                        
                        // Criar tab
                        this.createTab(filePath);
                        
                        // Definir como arquivo ativo
                        this.switchToFile(filePath);
                        
                        // Adicionar animação suave
                        const tab = document.querySelector(`.editor-tab[data-path="${filePath}"]`);
                        if (tab) {
                            tab.classList.add('tab-opening');
                            setTimeout(() => tab.classList.remove('tab-opening'), 300);
                        }
                        
                        // Atualizar informações
                        this.updateFileSize(content.length);
                        this.updateLanguageInfo(language);
                        
                    } catch (error) {
                        console.error('Erro ao abrir arquivo:', error);
                        this.showErrorState(error, `Tentando abrir: ${filePath}`);
                    } finally {
                        this.hideActivity();
                    }
                }
                
                // D.3 - Indicador visual arquivo aberto
                createTab(filePath) {
                    const tabsContainer = document.getElementById('editorTabs');
                    const fileName = filePath.split('/').pop();
                    
                    const tab = document.createElement('div');
                    tab.className = 'editor-tab';
                    tab.dataset.path = filePath;
                    tab.innerHTML = `
                        <span class="tab-icon">${this.getFileIcon(filePath)}</span>
                        <span class="tab-title">${fileName}</span>
                        <span class="tab-modified" style="display:none;">●</span>
                        <button class="tab-close" title="Fechar">×</button>
                    `;
                    
                    // Click na tab
                    tab.addEventListener('click', (e) => {
                        if (!e.target.classList.contains('tab-close')) {
                            this.switchToFile(filePath);
                        }
                    });
                    
                    // Fechar tab
                    tab.querySelector('.tab-close').addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.closeFile(filePath);
                    });
                    
                    tabsContainer.appendChild(tab);
                }
                
                // D.4 - Navegação entre arquivos
                switchToFile(filePath) {
                    const fileData = this.openFiles.get(filePath);
                    if (!fileData) return;
                    
                    // Salvar estado da view atual
                    if (this.currentFile) {
                        const currentData = this.openFiles.get(this.currentFile);
                        if (currentData) {
                            currentData.viewState = this.editor.saveViewState();
                        }
                    }
                    
                    // Trocar modelo
                    this.editor.setModel(fileData.model);
                    
                    // Restaurar view state
                    if (fileData.viewState) {
                        this.editor.restoreViewState(fileData.viewState);
                    }
                    
                    // Atualizar estado
                    this.currentFile = filePath;
                    
                    // Atualizar tabs visuais
                    this.updateTabsUI(filePath);
                    
                    // Atualizar explorer
                    this.highlightInExplorer(filePath);
                    
                    // Atualizar statusbar
                    this.updateFileInfo(filePath);
                }
                
                // D.5 - Criar/renomear/deletar arquivos
                async createNewFile(folderPath) {
                    const fileName = prompt('Nome do arquivo:');
                    if (!fileName) return;
                    
                    const filePath = `${folderPath}/${fileName}`;
                    
                    try {
                        const response = await fetch('/api/create-file', {
                            method: 'POST',
                            headers: {'Content-Type': 'application/json'},
                            body: JSON.stringify({path: filePath, content: ''})
                        });
                        
                        if (response.ok) {
                            // Atualizar explorer
                            this.refreshExplorer();
                            // Abrir arquivo novo
                            this.openFile(filePath);
                        }
                    } catch (error) {
                        console.error('Erro ao criar arquivo:', error);
                    }
                }
                
                // Helpers
                detectLanguage(filePath) {
                    const ext = filePath.split('.').pop().toLowerCase();
                    const languageMap = {
                        'py': 'python',
                        'js': 'javascript',
                        'ts': 'typescript',
                        'html': 'html',
                        'css': 'css',
                        'json': 'json',
                        'md': 'markdown',
                        'yaml': 'yaml',
                        'yml': 'yaml',
                        'xml': 'xml',
                        'sql': 'sql',
                        'sh': 'shell',
                        'bash': 'shell'
                    };
                    return languageMap[ext] || 'plaintext';
                }
                
                getFileIcon(filePath) {
                    const ext = filePath.split('.').pop().toLowerCase();
                    const iconMap = {
                        'py': '🐍',
                        'js': '📜',
                        'ts': '📘',
                        'html': '🌐',
                        'css': '🎨',
                        'json': '📋',
                        'md': '📝',
                        'txt': '📄',
                        'yaml': '⚙️',
                        'yml': '⚙️'
                    };
                    return iconMap[ext] || '📄';
                }
                
                updateTabsUI(activePath) {
                    document.querySelectorAll('.editor-tab').forEach(tab => {
                        if (tab.dataset.path === activePath) {
                            tab.classList.add('active');
                        } else {
                            tab.classList.remove('active');
                        }
                    });
                }
                
                highlightInExplorer(filePath) {
                    // Remover highlight anterior
                    document.querySelectorAll('.file-item').forEach(item => {
                        item.classList.remove('active-file');
                    });
                    
                    // Adicionar highlight no arquivo ativo
                    const activeItem = document.querySelector(`.file-item[data-path="${filePath}"]`);
                    if (activeItem) {
                        activeItem.classList.add('active-file');
                    }
                }
                
                closeFile(filePath) {
                    const fileData = this.openFiles.get(filePath);
                    if (!fileData) return;
                    
                    // Verificar se tem modificações
                    if (fileData.modified) {
                        if (!confirm(`Descartar alterações em ${filePath}?`)) {
                            return;
                        }
                    }
                    
                    // Remover tab
                    const tab = document.querySelector(`.editor-tab[data-path="${filePath}"]`);
                    if (tab) tab.remove();
                    
                    // Destruir modelo
                    fileData.model.dispose();
                    
                    // Remover dos arquivos abertos
                    this.openFiles.delete(filePath);
                    
                    // Se era o arquivo ativo, mudar para outro
                    if (this.currentFile === filePath) {
                        const remainingFiles = Array.from(this.openFiles.keys());
                        if (remainingFiles.length > 0) {
                            this.switchToFile(remainingFiles[remainingFiles.length - 1]);
                        } else {
                            // Nenhum arquivo aberto, mostrar welcome
                            this.showWelcome();
                        }
                    }
                }
                
                showWelcome() {
                    const welcomeModel = monaco.editor.createModel(
                        '# Bem-vindo ao Monaco Editor\n\n' +
                        '## Começar\n' +
                        '- Use o Explorer à esquerda para abrir arquivos\n' +
                        '- Pressione Ctrl+S para salvar\n' +
                        '- Pressione Ctrl+P para busca rápida\n\n' +
                        'print("Hello, Lilith!")',
                        'markdown'
                    );
                    this.editor.setModel(welcomeModel);
                    this.currentFile = null;
                }
                
                updateFileInfo(filePath) {
                    const fileName = filePath.split('/').pop();
                    const language = this.detectLanguage(filePath);
                    
                    // Atualizar statusbar
                    const statusItems = document.querySelectorAll('.status-item');
                    statusItems[2].textContent = language.charAt(0).toUpperCase() + language.slice(1);
                    
                    // Atualizar breadcrumbs
                    this.updateBreadcrumbs(filePath);
                }
                
                showError(message) {
                    // Por enquanto, console
                    console.error('❌', message);
                    // TODO: Implementar notificação visual
                }
                
                // C.2 - Breadcrumb navigation
                setupBreadcrumbs() {
                    // Adicionar container de breadcrumbs no HTML
                    const breadcrumbHtml = `
                        <div class="editor-breadcrumbs" id="editorBreadcrumbs">
                            <span class="breadcrumb-item">Workspace</span>
                        </div>
                    `;
                    
                    // Inserir após editor-header
                    const editorHeader = document.querySelector('.editor-header');
                    editorHeader.insertAdjacentHTML('afterend', breadcrumbHtml);
                }
                
                updateBreadcrumbs(filePath) {
                    if (!filePath) {
                        document.getElementById('editorBreadcrumbs').innerHTML = 
                            '<span class="breadcrumb-item">Workspace</span>';
                        return;
                    }
                    
                    const parts = filePath.split('/');
                    const breadcrumbs = parts.map((part, index) => {
                        const path = parts.slice(0, index + 1).join('/');
                        const isLast = index === parts.length - 1;
                        return `
                            <span class="breadcrumb-item ${isLast ? 'current' : ''}" 
                                  data-path="${path}"
                                  ${!isLast ? 'onclick="monacoWorkspace.navigateToBreadcrumb(\'' + path + '\')"' : ''}>
                                ${part}
                            </span>
                            ${!isLast ? '<span class="breadcrumb-separator">›</span>' : ''}
                        `;
                    }).join('');
                    
                    document.getElementById('editorBreadcrumbs').innerHTML = breadcrumbs;
                }
                
                // C.4 - Syntax highlighting melhorado
                setupAdvancedSyntax() {
                    // Registrar configurações específicas para Python
                    monaco.languages.setLanguageConfiguration('python', {
                        comments: {
                            lineComment: '#',
                            blockComment: ['"""', '"""']
                        },
                        brackets: [
                            ['{', '}'],
                            ['[', ']'],
                            ['(', ')']
                        ],
                        autoClosingPairs: [
                            { open: '{', close: '}' },
                            { open: '[', close: ']' },
                            { open: '(', close: ')' },
                            { open: '"', close: '"' },
                            { open: "'", close: "'" },
                            { open: '"""', close: '"""' }
                        ],
                        surroundingPairs: [
                            { open: '{', close: '}' },
                            { open: '[', close: ']' },
                            { open: '(', close: ')' },
                            { open: '"', close: '"' },
                            { open: "'", close: "'" }
                        ]
                    });
                    
                    // Adicionar snippets Python
                    monaco.languages.registerCompletionItemProvider('python', {
                        provideCompletionItems: (model, position) => {
                            const suggestions = [
                                {
                                    label: 'def',
                                    kind: monaco.languages.CompletionItemKind.Snippet,
                                    insertText: 'def ${1:function_name}(${2:params}):\n\t${3:pass}',
                                    insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                                    documentation: 'Define a function'
                                },
                                {
                                    label: 'class',
                                    kind: monaco.languages.CompletionItemKind.Snippet,
                                    insertText: 'class ${1:ClassName}:\n\tdef __init__(self${2:, params}):\n\t\t${3:pass}',
                                    insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                                    documentation: 'Define a class'
                                }
                            ];
                            return { suggestions };
                        }
                    });
                }
                
                // C.5 - Auto-save indicator
                setupAutoSave() {
                    // Monitorar mudanças
                    this.editor.onDidChangeModelContent(() => {
                        const filePath = this.currentFile;
                        if (!filePath) return;
                        
                        // Marcar como modificado
                        this.markAsDirty(filePath);
                        
                        // Reset auto-save timer
                        if (this.autoSaveEnabled) {
                            clearTimeout(this.autoSaveTimer);
                            this.autoSaveTimer = setTimeout(() => {
                                this.autoSaveFile(filePath);
                            }, this.autoSaveDelay);
                        }
                    });
                }
                
                markAsDirty(filePath) {
                    if (!this.isDirty.get(filePath)) {
                        this.isDirty.set(filePath, true);
                        
                        // Atualizar indicador visual na tab
                        const tab = document.querySelector(`.editor-tab[data-path="${filePath}"]`);
                        if (tab) {
                            tab.querySelector('.tab-modified').style.display = 'inline';
                        }
                        
                        // Atualizar statusbar
                        this.updateSaveIndicator('Não salvo');
                    }
                }
                
                markAsClean(filePath) {
                    this.isDirty.delete(filePath);
                    
                    // Remover indicador visual
                    const tab = document.querySelector(`.editor-tab[data-path="${filePath}"]`);
                    if (tab) {
                        tab.querySelector('.tab-modified').style.display = 'none';
                    }
                    
                    // Atualizar statusbar
                    this.updateSaveIndicator('Salvo');
                }
                
                // F.2 - Save file (Ctrl+S)
                async saveCurrentFile() {
                    if (!this.currentFile) return;
                    
                    const content = this.editor.getValue();
                    const filePath = this.currentFile;
                    
                    // Mostrar atividade
                    this.showActivity('saving');
                    
                    try {
                        this.updateSaveIndicator(false);
                        
                        const response = await this.apiCall('/api/save-file', {
                            method: 'POST',
                            body: JSON.stringify({
                                path: filePath,
                                content: content
                            })
                        });
                        
                        // Marcar como salvo
                        this.markAsClean(filePath);
                        
                        // Efeito visual de confirmação
                        this.flashSaveSuccess();
                        this.updateSaveIndicator(true);
                        
                        // Atualizar tamanho do arquivo
                        this.updateFileSize(content.length);
                        
                        // Feedback visual
                        this.showNotification(`✅ ${filePath.split('/').pop()} salvo`);
                        
                    } catch (error) {
                        console.error('Erro ao salvar:', error);
                        this.showNotification(`❌ Erro ao salvar: ${error.message}`, 'error');
                        this.updateSaveIndicator(false);
                    } finally {
                        this.hideActivity();
                    }
                }
                
                // F.3 - Save As dialog
                async saveAs() {
                    const newPath = prompt('Salvar como:', this.currentFile || 'novo_arquivo.py');
                    if (!newPath) return;
                    
                    const content = this.editor.getValue();
                    
                    try {
                        const response = await fetch('/api/save-file', {
                            method: 'POST',
                            headers: {'Content-Type': 'application/json'},
                            body: JSON.stringify({
                                path: newPath,
                                content: content
                            })
                        });
                        
                        if (!response.ok) throw new Error('Erro ao salvar');
                        
                        // Abrir o novo arquivo
                        this.openFile(newPath);
                        
                        // Fechar o antigo se era temporário
                        if (this.currentFile && this.currentFile.startsWith('untitled')) {
                            this.closeFile(this.currentFile);
                        }
                        
                    } catch (error) {
                        console.error('Erro ao salvar como:', error);
                        this.showNotification(`❌ Erro: ${error.message}`, 'error');
                    }
                }
                
                // F.5 - Undo/Redo stack
                setupUndoRedo() {
                    // O Monaco já tem undo/redo built-in
                    // Adicionar atalhos customizados se necessário
                    this.editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.KeyZ, () => {
                        this.editor.trigger('keyboard', 'redo', null);
                    });
                    
                    // Adicionar botões na UI
                    const undoRedoHtml = `
                        <div class="editor-actions">
                            <button class="action-btn" onclick="monacoWorkspace.editor.trigger('keyboard', 'undo', null)" title="Desfazer (Ctrl+Z)">↶</button>
                            <button class="action-btn" onclick="monacoWorkspace.editor.trigger('keyboard', 'redo', null)" title="Refazer (Ctrl+Y)">↷</button>
                        </div>
                    `;
                    
                    // Adicionar aos controles do editor
                    document.querySelector('.editor-header').insertAdjacentHTML('beforeend', undoRedoHtml);
                }
                
                // Auto-save
                async autoSaveFile(filePath) {
                    if (!this.isDirty.get(filePath)) return;
                    
                    console.log('⏱️ Auto-salvando...', filePath);
                    await this.saveCurrentFile();
                }
                
                // SPRINT 5 - WebSocket integration
                setupWebSocket() {
                    // Conectar ao WebSocket
                    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                    this.ws = new WebSocket(`${wsProtocol}//${window.location.host}/ws/file-watch`);
                    
                    this.ws.onopen = () => {
                        console.log('📡 WebSocket conectado');
                        this.updateConnectionStatus(true);
                    };
                    
                    this.ws.onmessage = (event) => {
                        const data = JSON.parse(event.data);
                        this.handleFileChange(data);
                    };
                    
                    this.ws.onerror = (error) => {
                        console.error('WebSocket erro:', error);
                        this.updateConnectionStatus(false);
                    };
                    
                    this.ws.onclose = () => {
                        console.log('📡 WebSocket desconectado');
                        this.updateConnectionStatus(false);
                        
                        // Reconectar após 5 segundos
                        setTimeout(() => this.setupWebSocket(), 5000);
                    };
                    
                    // Ping/pong para manter conexão viva
                    setInterval(() => {
                        if (this.ws.readyState === WebSocket.OPEN) {
                            this.ws.send('ping');
                        }
                    }, 30000);
                }
                
                handleFileChange(data) {
                    switch (data.type) {
                        case 'file_modified':
                            this.handleFileModified(data.path);
                            break;
                            
                        case 'file_created':
                            this.handleFileCreated(data.path, data.is_directory);
                            break;
                            
                        case 'file_deleted':
                            this.handleFileDeleted(data.path);
                            break;
                            
                        case 'file_moved':
                            this.handleFileMoved(data.old_path, data.new_path);
                            break;
                    }
                }
                
                handleFileModified(filePath) {
                    // Se o arquivo está aberto e não foi modificado localmente
                    const fileData = this.openFiles.get(filePath);
                    if (fileData && !this.isDirty.get(filePath)) {
                        // Mostrar notificação
                        this.showNotification(
                            `📝 ${filePath.split('/').pop()} foi modificado externamente. Recarregar?`,
                            'warning',
                            [
                                {
                                    label: 'Recarregar',
                                    action: () => this.reloadFile(filePath)
                                },
                                {
                                    label: 'Ignorar',
                                    action: () => {}
                                }
                            ]
                        );
                    }
                }
                
                handleFileCreated(filePath, isDirectory) {
                    // Atualizar explorer
                    this.refreshExplorerPartial(filePath);
                    
                    // Notificação sutil
                    if (!isDirectory) {
                        this.showNotification(`✨ Novo arquivo: ${filePath.split('/').pop()}`);
                    }
                }
                
                handleFileDeleted(filePath) {
                    // Se o arquivo está aberto
                    if (this.openFiles.has(filePath)) {
                        // Marcar como deletado
                        const tab = document.querySelector(`.editor-tab[data-path="${filePath}"]`);
                        if (tab) {
                            tab.classList.add('deleted');
                        }
                        
                        this.showNotification(
                            `🗑️ ${filePath.split('/').pop()} foi deletado`,
                            'error'
                        );
                    }
                    
                    // Atualizar explorer
                    this.refreshExplorerPartial(filePath);
                }
                
                async reloadFile(filePath) {
                    try {
                        const response = await this.apiCall('/api/read-file', {
                            method: 'POST',
                            body: JSON.stringify({path: filePath})
                        });
                        
                        const fileData = this.openFiles.get(filePath);
                        if (fileData) {
                            fileData.model.setValue(response.content);
                            this.isDirty.set(filePath, false);
                            this.updateTabState(filePath);
                        }
                    } catch (error) {
                        console.error('Erro ao recarregar arquivo:', error);
                    }
                }
                
                updateConnectionStatus(connected) {
                    const indicator = document.querySelector('.connection-status');
                    if (indicator) {
                        indicator.classList.toggle('connected', connected);
                        indicator.title = connected ? 'Conectado' : 'Desconectado';
                    }
                }
                
                // API Error handling
                async apiCall(url, options = {}) {
                    try {
                        const response = await fetch(url, {
                            ...options,
                            headers: {
                                'Content-Type': 'application/json',
                                ...options.headers
                            }
                        });
                        
                        const data = await response.json();
                        
                        if (!response.ok) {
                            throw new Error(data.message || `Erro ${response.status}`);
                        }
                        
                        return data;
                        
                    } catch (error) {
                        console.error(`API Error (${url}):`, error);
                        
                        // Mostrar erro ao usuário
                        this.showNotification(
                            `❌ Erro: ${error.message}`,
                            'error'
                        );
                        
                        throw error;
                    }
                }
                
                showNotification(message, type = 'info', actions = []) {
                    const notification = document.createElement('div');
                    notification.className = `editor-notification ${type}`;
                    notification.innerHTML = `
                        <div class="notification-content">${message}</div>
                        ${actions.length > 0 ? `
                            <div class="notification-actions">
                                ${actions.map(action => `
                                    <button class="notification-action" onclick="this.parentElement.parentElement.remove(); (${action.action})()">${action.label}</button>
                                `).join('')}
                            </div>
                        ` : ''}
                    `;
                    
                    const container = document.querySelector('.editor-notifications') || document.body;
                    container.appendChild(notification);
                    
                    // Auto-remover após 5 segundos se não houver ações
                    if (actions.length === 0) {
                        setTimeout(() => {
                            if (notification.parentElement) {
                                notification.remove();
                            }
                        }, 5000);
                    }
                }
                
                refreshExplorerPartial(filePath) {
                    // Implementar refresh parcial do explorer
                    console.log('🔄 Refresh parcial do explorer:', filePath);
                }
                
                // ============================================
                // SPRINT 6 - VISUAL POLISH (H.1→H.5)
                // ============================================
                
                // H.2 - Loading states
                showLoading(message = 'Carregando...') {
                    const loader = document.createElement('div');
                    loader.className = 'monaco-loader';
                    loader.id = 'monaco-loader';
                    loader.innerHTML = `
                        <div class="loader-content">
                            <div class="loader-spinner"></div>
                            <div class="loader-text">${message}</div>
                        </div>
                    `;
                    
                    document.getElementById('monacoContainer').appendChild(loader);
                }
                
                hideLoading() {
                    const loader = document.getElementById('monaco-loader');
                    if (loader) {
                        loader.classList.add('fade-out');
                        setTimeout(() => loader.remove(), 300);
                    }
                }
                
                // H.3 - Error states
                showErrorState(error, context = '') {
                    const errorContainer = document.createElement('div');
                    errorContainer.className = 'monaco-error-state';
                    errorContainer.innerHTML = `
                        <div class="error-icon">⚠️</div>
                        <div class="error-title">Ops! Algo deu errado</div>
                        <div class="error-message">${error.message || error}</div>
                        ${context ? `<div class="error-context">${context}</div>` : ''}
                        <div class="error-actions">
                            <button class="error-btn" onclick="location.reload()">Recarregar</button>
                            <button class="error-btn secondary" onclick="this.closest('.monaco-error-state').remove()">Fechar</button>
                        </div>
                    `;
                    
                    document.getElementById('monacoContainer').appendChild(errorContainer);
                }
                
                // H.4 - Status bar melhorado
                setupStatusBar() {
                    this.setupStatusBarInteractions();
                }
                
                setupStatusBarInteractions() {
                    // Language selector
                    const langSelector = document.querySelector('.language-selector');
                    if (langSelector) {
                        langSelector.addEventListener('click', () => {
                            this.showLanguageSelector();
                        });
                    }
                    
                    // Encoding selector  
                    const encodingSelector = document.querySelector('.encoding-selector');
                    if (encodingSelector) {
                        encodingSelector.addEventListener('click', () => {
                            this.showEncodingSelector();
                        });
                    }
                    
                    // Monitorar seleção
                    if (this.editor) {
                        this.editor.onDidChangeCursorSelection((e) => {
                            const selection = e.selection;
                            const selectionInfo = document.querySelector('.selection-info');
                            
                            if (selectionInfo) {
                                if (!selection.isEmpty()) {
                                    const text = this.editor.getModel().getValueInRange(selection);
                                    const chars = text.length;
                                    const lines = text.split('\n').length;
                                    
                                    selectionInfo.style.display = '';
                                    selectionInfo.querySelector('.status-text').textContent = 
                                        `Sel: ${chars} chars, ${lines} lines`;
                                } else {
                                    selectionInfo.style.display = 'none';
                                }
                            }
                        });
                    }
                }
                
                // H.5 - Activity indicators
                showActivity(type = 'loading') {
                    const spinner = document.querySelector('.activity-spinner');
                    const activities = {
                        loading: '⏳',
                        saving: '💾',
                        syncing: '🔄',
                        error: '❌'
                    };
                    
                    if (spinner) {
                        spinner.textContent = activities[type] || activities.loading;
                        spinner.style.display = 'inline-block';
                        spinner.className = `activity-spinner ${type}`;
                    }
                }
                
                hideActivity() {
                    const spinner = document.querySelector('.activity-spinner');
                    if (spinner) {
                        spinner.style.display = 'none';
                    }
                }
                
                // Language selector dialog
                showLanguageSelector() {
                    const languages = monaco.languages.getLanguages();
                    
                    const dialog = document.createElement('div');
                    dialog.className = 'language-selector-dialog';
                    dialog.innerHTML = `
                        <div class="dialog-header">Selecionar Linguagem</div>
                        <input type="text" class="dialog-search" placeholder="Buscar linguagem...">
                        <div class="language-list">
                            ${languages.map(lang => `
                                <div class="language-item" data-language="${lang.id}">
                                    ${lang.aliases ? lang.aliases[0] : lang.id}
                                </div>
                            `).join('')}
                        </div>
                    `;
                    
                    const overlay = document.createElement('div');
                    overlay.className = 'dialog-overlay';
                    overlay.appendChild(dialog);
                    document.body.appendChild(overlay);
                    
                    // Busca
                    const searchInput = dialog.querySelector('.dialog-search');
                    searchInput.addEventListener('input', (e) => {
                        const query = e.target.value.toLowerCase();
                        dialog.querySelectorAll('.language-item').forEach(item => {
                            const match = item.textContent.toLowerCase().includes(query);
                            item.style.display = match ? '' : 'none';
                        });
                    });
                    
                    // Seleção
                    dialog.querySelectorAll('.language-item').forEach(item => {
                        item.addEventListener('click', () => {
                            const language = item.dataset.language;
                            monaco.editor.setModelLanguage(this.editor.getModel(), language);
                            this.updateLanguageInfo(language);
                            overlay.remove();
                        });
                    });
                    
                    // Fechar
                    overlay.addEventListener('click', (e) => {
                        if (e.target === overlay) overlay.remove();
                    });
                    
                    searchInput.focus();
                }
                
                updateLanguageInfo(language) {
                    const langSelector = document.querySelector('.language-selector .status-text');
                    if (langSelector) {
                        langSelector.textContent = language.charAt(0).toUpperCase() + language.slice(1);
                    }
                }
                
                showEncodingSelector() {
                    // Implementar selector de encoding
                    console.log('🔤 Encoding selector (implementar)');
                }
                
                // Animação ao salvar
                flashSaveSuccess() {
                    const editor = document.getElementById('monacoContainer');
                    if (editor) {
                        editor.classList.add('save-flash');
                        setTimeout(() => editor.classList.remove('save-flash'), 200);
                    }
                }
                
                updateFileSize(size) {
                    const fileSizeElement = document.querySelector('.file-size .status-text');
                    if (fileSizeElement) {
                        const kb = Math.round(size / 1024);
                        fileSizeElement.textContent = `${kb} KB`;
                    }
                }
                
                updateCursorPosition(position) {
                    const cursorElement = document.querySelector('.cursor-position .status-text');
                    if (cursorElement) {
                        cursorElement.textContent = `Ln ${position.lineNumber}, Col ${position.column}`;
                    }
                }
                
                updateSaveIndicator(saved = true) {
                    const saveIndicator = document.querySelector('.save-indicator');
                    if (saveIndicator) {
                        const icon = saveIndicator.querySelector('.status-icon');
                        const text = saveIndicator.querySelector('.status-text');
                        
                        if (saved) {
                            icon.textContent = '💾';
                            text.textContent = 'Salvo';
                            saveIndicator.style.color = '#4ec9b0';
                        } else {
                            icon.textContent = '⚠️';
                            text.textContent = 'Não salvo';
                            saveIndicator.style.color = '#f0ad4e';
                        }
                    }
                }
            }
            
            // ============================================
            // SPRINT 7 - AI READY (J.1→J.5)
            // ============================================
            
            // Classe para gerenciar integração com AI
            class AIIntegration {
                constructor(workspace) {
                    this.workspace = workspace;
                    this.eventEmitter = new EventTarget();
                    this.contextCache = new Map();
                    
                    // Aguardar editor estar pronto
                    if (workspace.editor) {
                        this.setupEventSystem();
                    } else {
                        // Aguardar editor ser criado
                        setTimeout(() => {
                            if (workspace.editor) {
                                this.setupEventSystem();
                            }
                        }, 100);
                    }
                    
                    console.log('🤖 AI Integration inicializada');
                }
                
                // J.1 - Selection context API
                getSelectionContext() {
                    const editor = this.workspace.editor;
                    if (!editor) return null;
                    
                    const selection = editor.getSelection();
                    const model = editor.getModel();
                    
                    if (!model) return null;
                    
                    const selectedText = model.getValueInRange(selection);
                    const position = selection.getStartPosition();
                    
                    // Contexto expandido (5 linhas antes e depois)
                    const contextRange = new monaco.Range(
                        Math.max(1, position.lineNumber - 5),
                        1,
                        Math.min(model.getLineCount(), position.lineNumber + 5),
                        model.getLineMaxColumn(Math.min(model.getLineCount(), position.lineNumber + 5))
                    );
                    
                    const context = {
                        selectedText: selectedText,
                        hasSelection: !selection.isEmpty(),
                        selection: {
                            startLine: selection.startLineNumber,
                            startColumn: selection.startColumn,
                            endLine: selection.endLineNumber,
                            endColumn: selection.endColumn
                        },
                        surroundingCode: model.getValueInRange(contextRange),
                        language: model.getLanguageId(),
                        filePath: this.workspace.currentFile,
                        timestamp: Date.now()
                    };
                    
                    // Cache do contexto
                    this.contextCache.set('selection', context);
                    
                    return context;
                }
                
                // J.2 - Cursor position API
                getCursorContext() {
                    const editor = this.workspace.editor;
                    if (!editor) return null;
                    
                    const position = editor.getPosition();
                    const model = editor.getModel();
                    
                    if (!model || !position) return null;
                    
                    // Palavra atual sob o cursor
                    const wordAtPosition = model.getWordAtPosition(position);
                    
                    // Linha atual
                    const currentLine = model.getLineContent(position.lineNumber);
                    
                    // Contexto de função/classe
                    const scopeContext = this.findScopeContext(model, position);
                    
                    const context = {
                        position: {
                            line: position.lineNumber,
                            column: position.column
                        },
                        currentWord: wordAtPosition ? wordAtPosition.word : '',
                        currentLine: currentLine,
                        linePrefix: currentLine.substring(0, position.column - 1),
                        lineSuffix: currentLine.substring(position.column - 1),
                        scope: scopeContext,
                        indentLevel: this.getIndentLevel(currentLine),
                        language: model.getLanguageId()
                    };
                    
                    this.contextCache.set('cursor', context);
                    
                    return context;
                }
                
                // J.3 - File context API
                getFileContext() {
                    const editor = this.workspace.editor;
                    if (!editor) return null;
                    
                    const model = editor.getModel();
                    if (!model) return null;
                    
                    const content = model.getValue();
                    const lines = content.split('\\n');
                    
                    // Análise do arquivo
                    const analysis = {
                        totalLines: lines.length,
                        totalCharacters: content.length,
                        language: model.getLanguageId(),
                        filePath: this.workspace.currentFile,
                        fileName: this.workspace.currentFile ? this.workspace.currentFile.split('/').pop() : 'untitled',
                        imports: this.extractImports(content, model.getLanguageId()),
                        functions: this.extractFunctions(content, model.getLanguageId()),
                        classes: this.extractClasses(content, model.getLanguageId()),
                        todos: this.extractTodos(content),
                        modified: this.workspace.isDirty.get(this.workspace.currentFile) || false
                    };
                    
                    const context = {
                        content: content,
                        analysis: analysis,
                        openFiles: Array.from(this.workspace.openFiles.keys()),
                        timestamp: Date.now()
                    };
                    
                    this.contextCache.set('file', context);
                    
                    return context;
                }
                
                // J.4 - Code insertion API
                insertCode(code, options = {}) {
                    const editor = this.workspace.editor;
                    if (!editor) return false;
                    
                    const {
                        position = 'cursor',      // 'cursor', 'selection', 'endOfFile', 'line'
                        replace = false,          // substituir seleção
                        newLine = true,          // adicionar nova linha
                        autoIndent = true,       // auto-indentar
                        focus = true             // focar editor após inserir
                    } = options;
                    
                    try {
                        const model = editor.getModel();
                        if (!model) return false;
                        
                        let insertPosition;
                        let range;
                        
                        switch (position) {
                            case 'cursor':
                                insertPosition = editor.getPosition();
                                range = new monaco.Range(
                                    insertPosition.lineNumber,
                                    insertPosition.column,
                                    insertPosition.lineNumber,
                                    insertPosition.column
                                );
                                break;
                                
                            case 'selection':
                                const selection = editor.getSelection();
                                if (replace || selection.isEmpty()) {
                                    range = selection;
                                } else {
                                    insertPosition = selection.getEndPosition();
                                    range = new monaco.Range(
                                        insertPosition.lineNumber,
                                        insertPosition.column,
                                        insertPosition.lineNumber,
                                        insertPosition.column
                                    );
                                }
                                break;
                                
                            case 'endOfFile':
                                const lastLine = model.getLineCount();
                                const lastColumn = model.getLineMaxColumn(lastLine);
                                insertPosition = new monaco.Position(lastLine, lastColumn);
                                range = new monaco.Range(lastLine, lastColumn, lastLine, lastColumn);
                                break;
                                
                            case 'line':
                                if (options.lineNumber) {
                                    insertPosition = new monaco.Position(options.lineNumber, 1);
                                    range = new monaco.Range(
                                        options.lineNumber, 1,
                                        options.lineNumber, 1
                                    );
                                }
                                break;
                        }
                        
                        // Preparar código para inserção
                        let finalCode = code;
                        
                        if (autoIndent && insertPosition) {
                            const currentLine = model.getLineContent(insertPosition.lineNumber);
                            const indent = this.getIndentLevel(currentLine);
                            finalCode = this.applyIndent(code, indent);
                        }
                        
                        if (newLine && position !== 'endOfFile') {
                            finalCode = '\\n' + finalCode;
                        }
                        
                        // Executar inserção
                        editor.executeEdits('ai-insert', [{
                            range: range,
                            text: finalCode,
                            forceMoveMarkers: true
                        }]);
                        
                        // Focar e posicionar cursor
                        if (focus) {
                            editor.focus();
                            
                            // Posicionar cursor no final do código inserido
                            const lines = finalCode.split('\\n');
                            const newPosition = new monaco.Position(
                                range.startLineNumber + lines.length - 1,
                                lines[lines.length - 1].length + 1
                            );
                            editor.setPosition(newPosition);
                        }
                        
                        // Emitir evento
                        this.emitEvent('codeInserted', {
                            code: finalCode,
                            position: position,
                            options: options
                        });
                        
                        return true;
                        
                    } catch (error) {
                        console.error('Erro ao inserir código:', error);
                        return false;
                    }
                }
                
                // J.5 - Event system para chat
                setupEventSystem() {
                    const editor = this.workspace.editor;
                    if (!editor) return;
                    
                    // Mudança de seleção
                    editor.onDidChangeCursorSelection(() => {
                        this.emitEvent('selectionChanged', this.getSelectionContext());
                    });
                    
                    // Mudança de posição do cursor
                    editor.onDidChangeCursorPosition(() => {
                        this.emitEvent('cursorChanged', this.getCursorContext());
                    });
                    
                    // Mudança de conteúdo
                    editor.onDidChangeModelContent(() => {
                        this.emitEvent('contentChanged', {
                            fileContext: this.getFileContext(),
                            timestamp: Date.now()
                        });
                    });
                    
                    // Ações do usuário
                    editor.addAction({
                        id: 'ai.explainCode',
                        label: 'Explicar Código',
                        keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyE],
                        contextMenuGroupId: 'ai',
                        contextMenuOrder: 1,
                        run: () => {
                            this.emitEvent('action', {
                                type: 'explainCode',
                                context: this.getSelectionContext()
                            });
                        }
                    });
                    
                    editor.addAction({
                        id: 'ai.refactorCode',
                        label: 'Refatorar Código',
                        keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyR],
                        contextMenuGroupId: 'ai',
                        contextMenuOrder: 2,
                        run: () => {
                            this.emitEvent('action', {
                                type: 'refactorCode',
                                context: this.getSelectionContext()
                            });
                        }
                    });
                    
                    editor.addAction({
                        id: 'ai.generateCode',
                        label: 'Gerar Código',
                        keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyG],
                        contextMenuGroupId: 'ai',
                        contextMenuOrder: 3,
                        run: () => {
                            this.emitEvent('action', {
                                type: 'generateCode',
                                context: this.getCursorContext()
                            });
                        }
                    });
                }
                
                // Event emitter
                emitEvent(type, data) {
                    const event = new CustomEvent(`ai:${type}`, {
                        detail: data
                    });
                    this.eventEmitter.dispatchEvent(event);
                    
                    // Log para debug
                    console.log(`🤖 AI Event: ${type}`, data);
                }
                
                // Ouvir eventos
                on(type, callback) {
                    this.eventEmitter.addEventListener(`ai:${type}`, (e) => {
                        callback(e.detail);
                    });
                }
                
                // Helpers
                findScopeContext(model, position) {
                    const content = model.getValue();
                    const lines = content.split('\\n');
                    let currentIndent = Infinity;
                    let scope = {
                        type: 'global',
                        name: null,
                        startLine: 1
                    };
                    
                    // Procurar para trás por função ou classe
                    for (let i = position.lineNumber - 1; i >= 0; i--) {
                        const line = lines[i];
                        const indent = this.getIndentLevel(line);
                        
                        if (indent < currentIndent) {
                            if (line.match(/^\\s*def\\s+(\\w+)/)) {
                                const match = line.match(/def\\s+(\\w+)/);
                                scope = {
                                    type: 'function',
                                    name: match[1],
                                    startLine: i + 1
                                };
                                break;
                            } else if (line.match(/^\\s*class\\s+(\\w+)/)) {
                                const match = line.match(/class\\s+(\\w+)/);
                                scope = {
                                    type: 'class',
                                    name: match[1],
                                    startLine: i + 1
                                };
                                break;
                            }
                        }
                    }
                    
                    return scope;
                }
                
                getIndentLevel(line) {
                    const match = line.match(/^(\\s*)/);
                    return match ? match[1].length : 0;
                }
                
                applyIndent(code, indentLevel) {
                    const indent = ' '.repeat(indentLevel);
                    return code.split('\\n').map(line => indent + line).join('\\n');
                }
                
                extractImports(content, language) {
                    const imports = [];
                    const lines = content.split('\\n');
                    
                    const patterns = {
                        python: /^(?:from\\s+(\\S+)\\s+)?import\\s+(.+)$/,
                        javascript: /^import\\s+(.+)\\s+from\\s+['\"](.+)['\"];?$/,
                        typescript: /^import\\s+(.+)\\s+from\\s+['\"](.+)['\"];?$/
                    };
                    
                    const pattern = patterns[language];
                    if (!pattern) return imports;
                    
                    lines.forEach(line => {
                        const match = line.match(pattern);
                        if (match) {
                            imports.push(line.trim());
                        }
                    });
                    
                    return imports;
                }
                
                extractFunctions(content, language) {
                    const functions = [];
                    const patterns = {
                        python: /^\\s*def\\s+(\\w+)\\s*\\(/gm,
                        javascript: /^\\s*(?:async\\s+)?function\\s+(\\w+)\\s*\\(/gm,
                        typescript: /^\\s*(?:async\\s+)?function\\s+(\\w+)\\s*\\(/gm
                    };
                    
                    const pattern = patterns[language];
                    if (!pattern) return functions;
                    
                    let match;
                    while ((match = pattern.exec(content)) !== null) {
                        functions.push({
                            name: match[1],
                            line: content.substring(0, match.index).split('\\n').length
                        });
                    }
                    
                    return functions;
                }
                
                extractClasses(content, language) {
                    const classes = [];
                    const patterns = {
                        python: /^\\s*class\\s+(\\w+)/gm,
                        javascript: /^\\s*class\\s+(\\w+)/gm,
                        typescript: /^\\s*class\\s+(\\w+)/gm
                    };
                    
                    const pattern = patterns[language];
                    if (!pattern) return classes;
                    
                    let match;
                    while ((match = pattern.exec(content)) !== null) {
                        classes.push({
                            name: match[1],
                            line: content.substring(0, match.index).split('\\n').length
                        });
                    }
                    
                    return classes;
                }
                
                extractTodos(content) {
                    const todos = [];
                    const lines = content.split('\\n');
                    const pattern = /(?:TODO|FIXME|HACK|NOTE|XXX):\\s*(.+)/i;
                    
                    lines.forEach((line, index) => {
                        const match = line.match(pattern);
                        if (match) {
                            todos.push({
                                text: match[1],
                                line: index + 1,
                                type: match[0].split(':')[0].toUpperCase()
                            });
                        }
                    });
                    
                    return todos;
                }
            }
            
            // API PÚBLICA PARA O CHAT
            window.MonacoAIAPI = {
                // Obter contexto completo
                getContext: () => {
                    if (!window.monacoAI) return null;
                    return {
                        selection: window.monacoAI.getSelectionContext(),
                        cursor: window.monacoAI.getCursorContext(),
                        file: window.monacoAI.getFileContext()
                    };
                },
                
                // Inserir código
                insert: (code, options) => {
                    if (!window.monacoAI) return false;
                    return window.monacoAI.insertCode(code, options);
                },
                
                // Substituir seleção
                replace: (code) => {
                    if (!window.monacoAI) return false;
                    return window.monacoAI.insertCode(code, {
                        position: 'selection',
                        replace: true
                    });
                },
                
                // Ouvir eventos
                on: (event, callback) => {
                    if (!window.monacoAI) return;
                    window.monacoAI.on(event, callback);
                },
                
                // Ações rápidas
                explainSelection: () => {
                    if (!window.monacoAI) return;
                    const context = window.monacoAI.getSelectionContext();
                    window.monacoAI.emitEvent('action', {
                        type: 'explain',
                        context: context
                    });
                },
                
                refactorSelection: () => {
                    if (!window.monacoAI) return;
                    const context = window.monacoAI.getSelectionContext();
                    window.monacoAI.emitEvent('action', {
                        type: 'refactor',
                        context: context
                    });
                },
                
                generateAtCursor: (prompt) => {
                    if (!window.monacoAI) return;
                    const context = window.monacoAI.getCursorContext();
                    window.monacoAI.emitEvent('action', {
                        type: 'generate',
                        prompt: prompt,
                        context: context
                    });
                },
                
                // Testar se API está disponível
                isAvailable: () => {
                    return window.monacoAI !== undefined;
                }
            };
            
            // ============================================
            // SPRINT 4 - UX (E.1→E.5 + G.1→G.5)
            // ============================================
            
            // E.1→E.5 - Resize System
            class ResizeManager {
                constructor() {
                    this.isDragging = false;
                    this.currentDivider = null;
                    this.startX = 0;
                    this.startWidth = 0;
                    this.minWidth = 200;
                    this.maxWidth = 600;
                    this.init();
                }
                
                init() {
                    this.setupDividers();
                    this.setupEventListeners();
                }
                
                setupDividers() {
                    // Divider entre Explorer/Editor
                    const explorerDivider = document.createElement('div');
                    explorerDivider.className = 'resize-divider';
                    explorerDivider.id = 'explorer-divider';
                    explorerDivider.style.cssText = `
                        position: absolute;
                        top: 0;
                        left: 250px;
                        width: 4px;
                        height: 100%;
                        background: #333;
                        cursor: col-resize;
                        z-index: 1000;
                        opacity: 0;
                        transition: opacity 0.2s;
                    `;
                    
                    // Divider entre Editor/Chat
                    const editorDivider = document.createElement('div');
                    editorDivider.className = 'resize-divider';
                    editorDivider.id = 'editor-divider';
                    editorDivider.style.cssText = `
                        position: absolute;
                        top: 0;
                        right: 350px;
                        width: 4px;
                        height: 100%;
                        background: #333;
                        cursor: col-resize;
                        z-index: 1000;
                        opacity: 0;
                        transition: opacity 0.2s;
                    `;
                    
                    document.getElementById('vscode-container').appendChild(explorerDivider);
                    document.getElementById('vscode-container').appendChild(editorDivider);
                }
                
                setupEventListeners() {
                    const dividers = document.querySelectorAll('.resize-divider');
                    
                    dividers.forEach(divider => {
                        // E.5 - Cursor change on hover
                        divider.addEventListener('mouseenter', () => {
                            divider.style.opacity = '1';
                        });
                        
                        divider.addEventListener('mouseleave', () => {
                            if (!this.isDragging) {
                                divider.style.opacity = '0';
                            }
                        });
                        
                        divider.addEventListener('mousedown', (e) => {
                            this.startDrag(e, divider);
                        });
                    });
                    
                    document.addEventListener('mousemove', (e) => {
                        if (this.isDragging) {
                            this.doDrag(e);
                        }
                    });
                    
                    document.addEventListener('mouseup', () => {
                        this.endDrag();
                    });
                }
                
                startDrag(e, divider) {
                    this.isDragging = true;
                    this.currentDivider = divider;
                    this.startX = e.clientX;
                    
                    if (divider.id === 'explorer-divider') {
                        this.startWidth = parseInt(document.getElementById('vscode-explorer').style.width) || 250;
                    } else {
                        this.startWidth = parseInt(document.getElementById('vscode-chat').style.width) || 350;
                    }
                    
                    document.body.style.cursor = 'col-resize';
                    document.body.style.userSelect = 'none';
                    
                    e.preventDefault();
                }
                
                doDrag(e) {
                    if (!this.isDragging) return;
                    
                    const deltaX = e.clientX - this.startX;
                    
                    if (this.currentDivider.id === 'explorer-divider') {
                        // E.1 - Divider draggable entre explorer/editor
                        const newWidth = Math.max(this.minWidth, Math.min(this.maxWidth, this.startWidth + deltaX));
                        
                        document.getElementById('vscode-explorer').style.width = newWidth + 'px';
                        this.currentDivider.style.left = newWidth + 'px';
                    } else {
                        // E.2 - Divider draggable entre editor/chat
                        const newWidth = Math.max(this.minWidth, Math.min(this.maxWidth, this.startWidth - deltaX));
                        
                        document.getElementById('vscode-chat').style.width = newWidth + 'px';
                        this.currentDivider.style.right = newWidth + 'px';
                    }
                }
                
                endDrag() {
                    this.isDragging = false;
                    this.currentDivider = null;
                    document.body.style.cursor = '';
                    document.body.style.userSelect = '';
                    
                    // Esconder dividers
                    document.querySelectorAll('.resize-divider').forEach(div => {
                        div.style.opacity = '0';
                    });
                }
            }
            
            // G.1→G.5 - Shortcut System
            class ShortcutManager {
                constructor(monacoWorkspace) {
                    this.monaco = monacoWorkspace;
                    this.isFullscreen = false;
                    this.init();
                }
                
                init() {
                    this.setupKeyboardShortcuts();
                    this.createQuickOpenDialog();
                }
                
                setupKeyboardShortcuts() {
                    document.addEventListener('keydown', (e) => {
                        // G.2 - Ctrl+P quick open
                        if (e.ctrlKey && e.key === 'p') {
                            e.preventDefault();
                            this.showQuickOpen();
                        }
                        
                        // G.3 - Ctrl+F find/replace
                        if (e.ctrlKey && e.key === 'f') {
                            e.preventDefault();
                            this.showFindReplace();
                        }
                        
                        // G.4 - Ctrl+Tab switch tabs
                        if (e.ctrlKey && e.key === 'Tab') {
                            e.preventDefault();
                            this.switchTabs();
                        }
                        
                        // G.5 - F11 fullscreen editor
                        if (e.key === 'F11') {
                            e.preventDefault();
                            this.toggleFullscreen();
                        }
                        
                        // ESC - Fechar dialogs
                        if (e.key === 'Escape') {
                            this.hideQuickOpen();
                        }
                    });
                }
                
                createQuickOpenDialog() {
                    const dialog = document.createElement('div');
                    dialog.id = 'quick-open-dialog';
                    dialog.style.cssText = `
                        position: fixed;
                        top: 50px;
                        left: 50%;
                        transform: translateX(-50%);
                        width: 500px;
                        background: #2d2d30;
                        border: 1px solid #464647;
                        border-radius: 6px;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                        z-index: 10000;
                        display: none;
                    `;
                    
                    dialog.innerHTML = `
                        <input type="text" id="quick-open-input" placeholder="Digite para procurar arquivos..." style="
                            width: 100%;
                            padding: 10px;
                            background: #3c3c3c;
                            border: none;
                            color: #cccccc;
                            outline: none;
                            font-family: 'Inter', sans-serif;
                        ">
                        <div id="quick-open-results" style="
                            max-height: 300px;
                            overflow-y: auto;
                            border-top: 1px solid #464647;
                        "></div>
                    `;
                    
                    document.body.appendChild(dialog);
                    
                    // Event listeners
                    const input = document.getElementById('quick-open-input');
                    input.addEventListener('input', (e) => {
                        this.searchFiles(e.target.value);
                    });
                    
                    input.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter') {
                            this.selectFirstResult();
                        }
                    });
                }
                
                showQuickOpen() {
                    const dialog = document.getElementById('quick-open-dialog');
                    dialog.style.display = 'block';
                    document.getElementById('quick-open-input').focus();
                }
                
                hideQuickOpen() {
                    const dialog = document.getElementById('quick-open-dialog');
                    dialog.style.display = 'none';
                    document.getElementById('quick-open-input').value = '';
                    document.getElementById('quick-open-results').innerHTML = '';
                }
                
                async searchFiles(query) {
                    if (!query.trim()) {
                        document.getElementById('quick-open-results').innerHTML = '';
                        return;
                    }
                    
                    try {
                        const response = await fetch('/api/search-files', {
                            method: 'POST',
                            headers: {'Content-Type': 'application/json'},
                            body: JSON.stringify({query: query})
                        });
                        
                        const files = await response.json();
                        this.displaySearchResults(files);
                    } catch (error) {
                        console.error('Erro ao buscar arquivos:', error);
                    }
                }
                
                displaySearchResults(files) {
                    const resultsDiv = document.getElementById('quick-open-results');
                    
                    if (files.length === 0) {
                        resultsDiv.innerHTML = '<div style="padding: 10px; color: #888;">Nenhum arquivo encontrado</div>';
                        return;
                    }
                    
                    resultsDiv.innerHTML = files.map(file => `
                        <div class="quick-open-item" onclick="shortcutManager.openFile('${file}')" style="
                            padding: 8px 12px;
                            cursor: pointer;
                            border-bottom: 1px solid #333;
                            color: #cccccc;
                            font-size: 13px;
                        " onmouseover="this.style.background='#094771'" onmouseout="this.style.background='transparent'">
                            📄 ${file}
                        </div>
                    `).join('');
                }
                
                async openFile(filePath) {
                    await this.monaco.openFile(filePath);
                    this.hideQuickOpen();
                }
                
                selectFirstResult() {
                    const firstItem = document.querySelector('.quick-open-item');
                    if (firstItem) {
                        firstItem.click();
                    }
                }
                
                showFindReplace() {
                    // Usar o find/replace nativo do Monaco
                    if (this.monaco.editor) {
                        this.monaco.editor.getAction('actions.find').run();
                    }
                }
                
                switchTabs() {
                    const tabs = document.querySelectorAll('.editor-tab');
                    const currentTab = document.querySelector('.editor-tab.active');
                    
                    if (tabs.length <= 1) return;
                    
                    let nextIndex = 0;
                    if (currentTab) {
                        const currentIndex = Array.from(tabs).indexOf(currentTab);
                        nextIndex = (currentIndex + 1) % tabs.length;
                    }
                    
                    tabs[nextIndex].click();
                }
                
                toggleFullscreen() {
                    const container = document.getElementById('vscode-container');
                    const editor = document.getElementById('vscode-editor');
                    
                    if (!this.isFullscreen) {
                        // Entrar em fullscreen
                        container.classList.add('fullscreen-mode');
                        editor.style.width = '100%';
                        document.getElementById('vscode-explorer').style.display = 'none';
                        document.getElementById('vscode-chat').style.display = 'none';
                        this.isFullscreen = true;
                    } else {
                        // Sair do fullscreen
                        container.classList.remove('fullscreen-mode');
                        editor.style.width = '';
                        document.getElementById('vscode-explorer').style.display = 'block';
                        document.getElementById('vscode-chat').style.display = 'block';
                        this.isFullscreen = false;
                    }
                    
                    // Redimensionar editor
                    if (this.monaco.editor) {
                        setTimeout(() => {
                            this.monaco.editor.layout();
                        }, 100);
                    }
                }
            }
                
                // Helpers
                updateSaveIndicator(status) {
                    const statusBar = document.querySelector('.editor-statusbar');
                    let saveIndicator = statusBar.querySelector('.save-indicator');
                    
                    if (!saveIndicator) {
                        saveIndicator = document.createElement('span');
                        saveIndicator.className = 'status-item save-indicator';
                        statusBar.insertBefore(saveIndicator, statusBar.firstChild);
                    }
                    
                    saveIndicator.textContent = status;
                    
                    // Adicionar classe para animação
                    if (status === 'Salvando...') {
                        saveIndicator.classList.add('saving');
                    } else {
                        saveIndicator.classList.remove('saving');
                    }
                }
                
                showNotification(message, type = 'info') {
                    // Criar elemento de notificação
                    const notification = document.createElement('div');
                    notification.className = `editor-notification ${type}`;
                    notification.textContent = message;
                    
                    // Adicionar ao editor
                    document.querySelector('.editor-column').appendChild(notification);
                    
                    // Remover após 3 segundos
                    setTimeout(() => {
                        notification.classList.add('fade-out');
                        setTimeout(() => notification.remove(), 300);
                    }, 3000);
                }
            }


            async function updateLogs() {
                const level = document.getElementById('logLevelFilter').value;
                const module = document.getElementById('logModuleFilter').value;
                const model = document.getElementById('logModelFilter').value;
                const search = document.getElementById('logSearchFilter').value;
                
                try {
                    const params = new URLSearchParams({ limit: 100 });
                    if (level) params.append('level', level);
                    if (module) params.append('module', module);
                    if (model) params.append('model', model);
                    if (search) params.append('search', search);
                    
                    const response = await fetch(`/api/logs?${params}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        displayLogs(data.logs);
                        updateLogsStats();
                    }
                } catch (error) {
                    console.error('Error loading logs:', error);
                }
            }

            function displayLogs(logs) {
                const tbody = document.getElementById('logsTableBody');
                
                if (logs.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; padding: 40px; color: #666;">No logs found</td></tr>';
                    return;
                }
                
                tbody.innerHTML = logs.map(log => {
                    const time = log.timestamp.split(' ')[1]; // Apenas hora
                    const model = log.details?.model || '-';
                    const modelShort = model.includes(':') ? model.split(':')[0] : model;
                    
                    return `
                        <tr>
                            <td style="color: #666; font-size: 11px;">${time}</td>
                            <td class="log-level-${log.level}">${log.level}</td>
                            <td>${log.module}</td>
                            <td style="color: #9C27B0; font-size: 11px;">${modelShort}</td>
                            <td style="color: #ccc;">${escapeHtml(log.message)}</td>
                        </tr>
                    `;
                }).join('');
            }

            async function updateLogsStats() {
                try {
                    const response = await fetch('/api/logs/stats');
                    const data = await response.json();
                    
                    if (data.success) {
                        const stats = data.stats;
                        let statsHtml = `<span>📊 Total: ${stats.total}</span>`;
                        
                        Object.entries(stats.by_level || {}).forEach(([level, count]) => {
                            const icon = level === 'ERROR' ? '❌' : level === 'WARNING' ? '⚠️' : '✅';
                            statsHtml += `<span>${icon} ${count}</span>`;
                        });
                        
                        document.getElementById('logsStats').innerHTML = statsHtml;
                    }
                } catch (error) {
                    console.error('Error loading stats:', error);
                }
            }

            async function clearAllLogs() {
                if (!confirm('Clear all logs?')) return;

                try {
                    await fetch('/api/logs/clear', { method: 'POST' });
                    updateLogs();
                } catch (error) {
                    console.error('Error clearing logs:', error);
                }
            }

            // NOVAS FUNÇÕES DE LIMPEZA
            function clearCurrentChat() {
                if (!confirm('🧹 Clear current chat conversation?')) return;

                // Limpar mensagens da tela atual
                const messagesInner = document.getElementById('messagesInner');
                if (messagesInner) {
                    messagesInner.innerHTML = '';
                    console.log('✅ Chat atual limpo');
                }

                // Fechar dropdown
                document.getElementById('clearDropdown').style.display = 'none';
            }

            async function clearLogsAndConversations() {
                if (!confirm('💥 Clear EVERYTHING (Logs + Conversations)? This is IRREVERSIBLE!')) return;

                try {
                    // Limpar logs
                    await fetch('/api/logs/clear', { method: 'POST' });

                    // Limpar conversas
                    await fetch('/api/conversations/clear', { method: 'POST' });

                    // Limpar chat atual
                    clearCurrentChat();

                    // Atualizar displays
                    updateLogs();

                    console.log('💥 TUDO LIMPO!');
                    alert('💥 Everything cleared successfully!');
                } catch (error) {
                    console.error('Error clearing everything:', error);
                    alert('❌ Error clearing data');
                }

                // Fechar dropdown
                document.getElementById('clearDropdown').style.display = 'none';
            }

            async function quickClearAll() {
                if (!confirm('⚡ Quick Clear All? (Chat + Logs + Conversations)')) return;

                try {
                    // Executar todas as limpezas rapidamente
                    const promises = [
                        fetch('/api/logs/clear', { method: 'POST' }),
                        fetch('/api/conversations/clear', { method: 'POST' })
                    ];

                    await Promise.all(promises);

                    // Limpar chat atual
                    clearCurrentChat();

                    // Atualizar
                    updateLogs();

                    console.log('⚡ Quick Clear All completed!');
                    alert('⚡ Everything cleared quickly!');
                } catch (error) {
                    console.error('Error in quick clear:', error);
                    alert('❌ Error in quick clear');
                }

                // Fechar dropdown
                document.getElementById('clearDropdown').style.display = 'none';
            }

            function startLogsAutoRefresh() {
                stopLogsAutoRefresh();
                logsRefreshInterval = setInterval(updateLogs, 2000);
            }

            function stopLogsAutoRefresh() {
                if (logsRefreshInterval) {
                    clearInterval(logsRefreshInterval);
                }
            }

            function escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            let currentTab = 'logs';

            function showLogsTab(tab) {
                currentTab = tab;
                
                // Atualizar botões
                document.querySelectorAll('.log-tab').forEach(btn => {
                    btn.classList.remove('active');
                });
                event.target.classList.add('active');
                
                // Mostrar conteúdo apropriado
                if (tab === 'logs') {
                    updateLogs();
                } else if (tab === 'conversations') {
                    loadConversations();
                } else if (tab === 'cascade') {
                    loadCascadeView();
                }
            }

            async function loadConversations() {
                try {
                    const response = await fetch('/api/conversations');
                    const data = await response.json();
                    
                    if (data.success) {
                        displayConversations(data.conversations);
                    }
                } catch (error) {
                    console.error('Error loading conversations:', error);
                }
            }

            function displayConversations(conversations) {
                const content = document.querySelector('.logs-content');
                
                let html = '<div class="conversations-container">';
                
                // Cabeçalho com botões de controle
                html += `
                    <div class="conversations-header">
                        <div class="conv-info">
                            <span>💬 ${conversations.length} conversas encontradas</span>
                        </div>
                        <div class="conv-controls">
                            <button class="btn-small" onclick="loadConversations()">🔄 Atualizar</button>
                            <button class="btn-small btn-danger" onclick="clearAllConversations()">🗑️ Limpar Tudo</button>
                        </div>
                    </div>
                `;
                
                conversations.forEach(conv => {
                    html += `
                        <div class="conversation-view">
                            <div class="conv-header">
                                <span class="timestamp">${conv.timestamp}</span>
                                <span class="model-badge">${conv.model}</span>
                            </div>
                            
                            <div class="conv-messages">
                                <div class="user-msg">
                                    <strong>👤 Usuário:</strong> ${escapeHtml(conv.user_message)}
                                </div>
                                <div class="assistant-msg">
                                    <strong>🤖 ${conv.model}:</strong> ${escapeHtml(conv.assistant_message)}
                                </div>
                            </div>
                            
                            ${conv.cascade_analyses && conv.cascade_analyses.length > 0 ? `
                                <div class="cascade-analysis">
                                    <h4>🔗 Análises em Cascata:</h4>
                                    ${conv.cascade_analyses.map(a => `
                                        <div class="analysis-item">
                                            <span class="model-badge">${a.model}</span>
                                            <p>${escapeHtml(a.analysis)}</p>
                                        </div>
                                    `).join('')}
                                </div>
                            ` : `
                                <button class="btn-small" onclick="startCascadeAnalysis('${conv.timestamp}', '${conv.model}')">
                                    🔗 Iniciar Análise em Cascata
                                </button>
                            `}
                        </div>
                    `;
                });
                
                html += '</div>';
                content.innerHTML = html;
            }

            async function startCascadeAnalysis(timestamp, model) {
                // Encontrar conversa
                const response = await fetch('/api/conversations?limit=50');
                const data = await response.json();
                
                const conv = data.conversations.find(c => 
                    c.timestamp === timestamp && c.model === model
                );
                
                if (conv) {
                    const analysisResponse = await fetch('/api/analyze-cascade', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({
                            user_message: conv.user_message,
                            assistant_message: conv.assistant_message,
                            model: conv.model
                        })
                    });
                    
                    const result = await analysisResponse.json();
                    if (result.success) {
                        loadConversations(); // Recarregar para mostrar análises
                    }
                }
            }

            function loadCascadeView() {
                const content = document.querySelector('.logs-content');
                content.innerHTML = `
                    <div class="cascade-overview">
                        <h3>🔗 Análise em Cascata</h3>
                        <p>Análises colaborativas em desenvolvimento...</p>
                        <button class="btn-small" onclick="loadConversations()">Ver Conversas com Análises</button>
                    </div>
                `;
            }


            async function clearAllConversations() {
                console.log('🗑️ Executando clearAllConversations...');
                // Confirmar ação perigosa
                const confirmText = `⚠️  ATENÇÃO: Esta ação é IRREVERSÍVEL!

Esta operação irá:
✗ Apagar TODAS as conversas salvas
✗ Resetar completamente o contexto das IAs
✗ Remover histórico de análises em cascata

As próximas conversas começarão do ZERO, sem memória.

Digite "CONFIRMAR" ou "CONFIRMA" para prosseguir:`;

                const userInput = prompt(confirmText);

                if (!userInput || (userInput.toUpperCase() !== "CONFIRMAR" && userInput.toUpperCase() !== "CONFIRMA")) {
                    alert("❌ Operação cancelada");
                    return;
                }
                
                try {
                    // Obter contagem atual
                    const countResponse = await fetch('/api/conversations/count');
                    const countData = await countResponse.json();
                    const currentCount = countData.count || 0;
                    
                    if (currentCount === 0) {
                        alert("ℹ️ Não há conversas para limpar");
                        return;
                    }
                    
                    // Executar limpeza
                    const response = await fetch('/api/conversations/clear', {
                        method: 'POST'
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        alert(`✅ ${result.conversations_cleared} conversas removidas!
                        
🔄 O contexto foi resetado completamente.
🆕 As próximas conversas começarão do zero.`);
                        
                        // Recarregar lista de conversas
                        loadConversations();
                    } else {
                        alert(`❌ Erro: ${result.error}`);
                    }
                } catch (error) {
                    console.error('Error clearing conversations:', error);
                    alert(`❌ Erro na operação: ${error.message}`);
                }
            }

            function toggleClearDropdown() {
                const dropdown = document.getElementById('clearDropdown');
                dropdown.classList.toggle('show');
                
                // Fechar dropdown ao clicar fora
                document.addEventListener('click', function(event) {
                    if (!event.target.closest('.dropdown')) {
                        dropdown.classList.remove('show');
                    }
                });
            }

            async function clearConversationsToday() {
                console.log('🗑️ Executando clearConversationsToday...');
                const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
                console.log('📅 Data de hoje:', today);
                await clearConversationsByDate(today, 'hoje');
            }

            async function clearConversationsYesterday() {
                const yesterday = new Date();
                yesterday.setDate(yesterday.getDate() - 1);
                const yesterdayStr = yesterday.toISOString().split('T')[0];
                await clearConversationsByDate(yesterdayStr, 'ontem');
            }

            async function clearConversationsByDate(targetDate, displayName) {
                console.log('🗑️ Executando clearConversationsByDate...', targetDate, displayName);
                const confirmText = `⚠️  ATENÇÃO: Limpar conversas de ${displayName}!

Esta operação irá:
✗ Apagar conversas do dia ${targetDate}
✗ Remover contexto desse dia específico
✗ Manter todas as outras conversas

Digite "CONFIRMAR" ou "CONFIRMA" para prosseguir:`;

                const userInput = prompt(confirmText);

                if (!userInput || (userInput.toUpperCase() !== "CONFIRMAR" && userInput.toUpperCase() !== "CONFIRMA")) {
                    alert("❌ Operação cancelada");
                    return;
                }

                try {
                    const response = await fetch('/api/conversations/clear-date', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({date: targetDate})
                    });

                    const result = await response.json();

                    if (result.success) {
                        alert(`✅ ${result.conversations_cleared} conversas de ${displayName} removidas!`);
                        if (currentTab === 'conversations') {
                            loadConversations();
                        }
                    } else {
                        alert(`❌ Erro: ${result.error}`);
                    }

                } catch (error) {
                    console.error('Error clearing conversations by date:', error);
                    alert(`❌ Erro na operação: ${error.message}`);
                }
            }

            async function showClearByDateModal() {
                // Buscar datas disponíveis
                try {
                    const response = await fetch('/api/conversations/by-date');
                    const data = await response.json();
                    
                    if (!data.success || Object.keys(data.dates).length === 0) {
                        alert("ℹ️ Não há conversas para limpar");
                        return;
                    }
                    
                    // Criar lista de datas
                    let datesText = "Datas disponíveis:\\n";
                    Object.keys(data.dates).sort().forEach(date => {
                        datesText += `📅 ${date}: ${data.dates[date]} conversas\\n`;
                    });
                    
                    const selectedDate = prompt(`${datesText}\\nDigite a data que deseja limpar (YYYY-MM-DD):`);
                    
                    if (selectedDate && data.dates[selectedDate]) {
                        await clearConversationsByDate(selectedDate, selectedDate);
                    } else if (selectedDate) {
                        alert("❌ Data não encontrada ou não possui conversas");
                    }
                    
                } catch (error) {
                    console.error('Error fetching dates:', error);
                    alert("❌ Erro ao buscar datas disponíveis");
                }
            }
        </script>

        <!-- LOGS PANEL -->
        <div id="logsPanel" class="logs-panel">
            <div class="logs-header">
                <h2 style="color: #202020; font-weight: 300;">System Logs</h2>
                <button class="logs-close" onclick="toggleLogsPanel()">×</button>
                <div class="logs-tabs">
                    <button class="log-tab active" onclick="showLogsTab('logs')">📊 Logs</button>
                    <button class="log-tab" onclick="showLogsTab('conversations')">💬 Conversas</button>
                    <button class="log-tab" onclick="showLogsTab('cascade')">🔗 Análise em Cascata</button>
                </div>
            </div>
            
            <div class="logs-filters">
                <select id="logLevelFilter" class="log-filter" onchange="updateLogs()">
                    <option value="">All Levels</option>
                    <option value="INFO">INFO</option>
                    <option value="WARNING">WARNING</option>
                    <option value="ERROR">ERROR</option>
                    <option value="DEBUG">DEBUG</option>
                </select>
                
                <select id="logModuleFilter" class="log-filter" onchange="updateLogs()">
                    <option value="">All Modules</option>
                    <option value="chat">Chat</option>
                    <option value="system">System</option>
                    <option value="postgres">PostgreSQL</option>
                </select>
                
                <select id="logModelFilter" class="log-filter" onchange="updateLogs()">
                    <option value="">All Models</option>
                </select>
                
                <input type="text" id="logSearchFilter" class="log-filter" placeholder="Search..." onkeyup="updateLogs()" style="flex: 1;">
                
                <button class="btn btn-danger" onclick="clearAllLogs()">Clear Logs</button>

                <button class="btn btn-success" onclick="reconnectAIs()" style="margin-left: 10px;">
                    🔌 Reconnect AIs
                </button>
                
                <div class="dropdown" style="margin-left: 10px; position: relative; display: inline-block;">
                    <button class="btn btn-danger dropdown-toggle" onclick="toggleClearDropdown()">
                        🗑️ Clear Conversas ▼
                    </button>
                    <div id="clearDropdown" class="dropdown-menu">
                        <a href="#" onclick="clearCurrentChat()">🧹 Clear Chat Atual</a>
                        <a href="#" onclick="clearConversationsToday()">📅 Clear Hoje</a>
                        <a href="#" onclick="clearConversationsYesterday()">📅 Clear Ontem</a>
                        <a href="#" onclick="showClearByDateModal()">📅 Clear Data Específica</a>
                        <div class="dropdown-divider"></div>
                        <a href="#" onclick="clearAllConversations()" class="danger">🗑️ Clear Todas (IRREVERSÍVEL)</a>
                        <a href="#" onclick="clearLogsAndConversations()" class="danger">💥 Clear TUDO (Logs + Conversas)</a>
                        <a href="#" onclick="quickClearAll()" class="danger">⚡ Quick Clear All</a>
                    </div>
                </div>
            </div>
            
            <div class="logs-content">
                <div class="logs-table">
                    <table>
                        <thead>
                            <tr>
                                <th width="120">Time</th>
                                <th width="80">Level</th>
                                <th width="100">Module</th>
                                <th width="150">Model</th>
                                <th>Message</th>
                            </tr>
                        </thead>
                        <tbody id="logsTableBody">
                            <tr><td colspan="5" style="text-align: center; padding: 40px; color: #666;">Loading logs...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div class="logs-stats" id="logsStats">
                <span>📊 Total: 0</span>
            </div>
        </div>

        <!-- MONACO EDITOR PANEL -->
        <div id="monacoPanel" class="monaco-panel">
            <div class="monaco-container">
                <div id="mainWorkspace" class="main-workspace">
                    <!-- Coluna 1: Explorer (preservar existente) -->
                    <div id="explorerColumn" class="workspace-column explorer-column">
                        <div class="explorer-header">
                            <span>EXPLORER</span>
                            <button class="explorer-close" onclick="toggleMonacoPanel()">×</button>
                        </div>
                        <div class="explorer-content">
                            <div class="file-item" data-path="./test.py">
                                <span class="file-icon">🐍</span>
                                <span class="file-name">test.py</span>
                            </div>
                            <div class="file-item" data-path="./example.js">
                                <span class="file-icon">📜</span>
                                <span class="file-name">example.js</span>
                            </div>
                            <div class="file-item" data-path="./readme.md">
                                <span class="file-icon">📝</span>
                                <span class="file-name">readme.md</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Divider 1 -->
                    <div class="column-divider" id="divider1"></div>
                    
                    <!-- Coluna 2: Monaco Editor -->
                    <div id="editorColumn" class="workspace-column editor-column">
                        <div class="editor-header">
                            <div class="editor-tabs" id="editorTabs">
                                <!-- Tabs serão adicionadas dinamicamente -->
                            </div>
                        </div>
                        <div id="monacoContainer" class="monaco-container-inner">
                            <!-- Monaco será inicializado aqui -->
                        </div>
                        <div class="editor-statusbar">
                            <span class="status-item save-indicator">
                                <span class="status-icon">💾</span>
                                <span class="status-text">Salvo</span>
                            </span>
                            <span class="status-item encoding-selector" title="Clique para mudar">
                                <span class="status-text">UTF-8</span>
                            </span>
                            <span class="status-item language-selector" title="Clique para mudar">
                                <span class="status-text">Python</span>
                            </span>
                            <span class="status-item cursor-position">
                                <span class="status-text">Ln 1, Col 1</span>
                            </span>
                            <span class="status-item selection-info" style="display: none;">
                                <span class="status-text">Sel: 0</span>
                            </span>
                            <span class="status-item file-size">
                                <span class="status-text">0 KB</span>
                            </span>
                            <div class="status-spacer"></div>
                            <span class="status-item connection-indicator">
                                <span class="connection-status" title="Desconectado"></span>
                            </span>
                            <span class="status-item activity-indicator">
                                <span class="activity-spinner" style="display: none;"></span>
                            </span>
                        </div>
                    </div>
                    
                    <!-- Divider 2 -->
                    <div class="column-divider" id="divider2"></div>
                    
                    <!-- Coluna 3: AI Chat (futura) -->
                    <div id="chatColumn" class="workspace-column chat-column">
                        <div class="chat-placeholder">
                            <p>AI Chat (Em breve)</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- MULTI-CHAT PANEL -->
        <div id="multiChatPanel" class="multi-chat-panel">
            <div class="multi-chat-header">
                <h2>🤖 Conversa Multi-IA</h2>
                <button class="logs-close" onclick="toggleMultiChatPanel()">×</button>
            </div>
            
            <div class="multi-chat-content">
                <div class="model-selection" id="modelSelectionContainer">
                    <h3>Carregando modelos...</h3>
                </div>
                
                <div class="conversation-display" id="conversationDisplay">
                    <div style="text-align: center; color: #666; padding: 50px;">
                        Configure os modelos e inicie uma conversa para ver as IAs conversando entre si
                    </div>
                </div>
            </div>
            
            <div class="multi-chat-controls">
                <textarea 
                    id="multiChatInput" 
                    class="multi-chat-input" 
                    placeholder="Digite a mensagem inicial para as IAs discutirem..."
                ></textarea>
                
                <div class="rounds-control">
                    <label>Rodadas:</label>
                    <input type="number" id="roundsInput" class="rounds-input" value="3" min="1" max="10">
                </div>
                
                <button id="startMultiChatBtn" class="btn-start-chat" onclick="startMultiChat()" disabled>
                    Iniciar Conversa
                </button>
            </div>
        </div>
        
        
        <script src="/static/markdown.js"></script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.get("/health")
async def health_check():
    """Health check do sistema completo"""
    ollama_status = chat_instance.check_connection()
    
    return {
        "status": "ok",
        "system": "online",
        "ollama_connected": ollama_status
    }

@app.get("/models")
async def get_models():
    """Retorna lista de TODOS os modelos disponíveis (Ollama + APIs externas)"""
    all_models = []
    
    # Modelos locais do Ollama
    local_models = []
    if chat_instance.check_connection():
        local_models = chat_instance.list_models()
        if local_models:
            all_models.extend([{"name": model, "provider": "ollama", "type": "local"} for model in local_models])
    
    # Modelos externos das APIs
    external_models = []
    if unified_ai:
        external_models = unified_ai.get_available_external_models()
        all_models.extend([{"name": model, "provider": "external", "type": "cloud"} for model in external_models])
    
    # Se não tem nenhum modelo, retorna padrão
    if not all_models:
        return [{"name": "llama3:8b", "provider": "ollama", "type": "local"}]
    
    # Log dos modelos carregados
    print(f"🔍 DEBUG: Modelos totais disponíveis: {len(all_models)}")
    print(f"🔍 DEBUG: Locais: {len(local_models)}, Externos: {len(external_models)}")
    
    return all_models

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(message: ChatMessage):
    """Endpoint para enviar mensagens ao chat integrado"""
    try:
        print(f"🔍 DEBUG: Recebido - {message.message[:50]}...")
        print(f"🔍 DEBUG: Modelo solicitado - {message.model}")
        
        if not chat_instance.check_connection():
            raise HTTPException(status_code=503, detail="Ollama não está acessível")

        if not message.message.strip():
            raise HTTPException(status_code=400, detail="Mensagem não pode estar vazia")

        # Usar o modelo selecionado pelo usuário
        model_to_use = message.model or "llama3:8b"
        print(f"🔍 DEBUG: Enviando para Ollama com modelo {model_to_use}")
        
        # CONTEXTO ACUMULATIVO - Construir contexto completo da conversa
        conversation_context = ""
        if log_manager:
            try:
                # Buscar últimas conversas (até 10k linhas de contexto)
                recent_conversations = log_manager.get_full_conversations(50)  # últimas 50 conversas
                
                context_lines = []
                for conv in reversed(recent_conversations):  # mais antigas primeiro
                    context_lines.append(f"Usuário: {conv['user_message']}")
                    context_lines.append(f"{conv['model']}: {conv['assistant_message']}")
                
                # Limitar a 10.000 linhas máximo
                if len(context_lines) > 10000:
                    context_lines = context_lines[-10000:]
                
                if context_lines:
                    conversation_context = "\n".join(context_lines) + "\n\n"
                    print(f"🔍 DEBUG: Contexto com {len(context_lines)} linhas carregado")
                
            except Exception as e:
                print(f"🔍 DEBUG: Erro ao carregar contexto: {e}")
                conversation_context = ""
        
        # Construir prompt completo com contexto
        if conversation_context:
            full_prompt = f"""HISTÓRICO DA CONVERSA:
{conversation_context}NOVA MENSAGEM DO USUÁRIO: {message.message}

Como {model_to_use}, continue esta conversa naturalmente:"""
        else:
            full_prompt = message.message
        
        # LOGS INTEGRATION - Log de mensagem recebida
        if log_manager:
            log_manager.add_log(
                "INFO", 
                "chat", 
                f"Mensagem recebida do usuário",
                {"user": message.message[:100], "model": model_to_use, "context_lines": len(conversation_context.split('\n')) if conversation_context else 0}
            )
        
        # ENVIAR MENSAGEM - Ollama ou API externa
        print(f"🔍 DEBUG: unified_ai disponível: {unified_ai is not None}")
        if unified_ai:
            print(f"🔍 DEBUG: Modelos externos disponíveis: {unified_ai.get_available_external_models()}")
            print(f"🔍 DEBUG: É modelo externo? {unified_ai.is_external_model(model_to_use)}")

        if unified_ai and unified_ai.is_external_model(model_to_use):
            # Usar API externa
            print(f"🔍 DEBUG: Enviando para API externa: {model_to_use}")
            response = await unified_ai.send_message(full_prompt, model_to_use)
            print(f"🔍 DEBUG: Resposta da API externa - {response[:100] if response else 'None'}...")
        else:
            # Usar Ollama local
            print(f"🔍 DEBUG: Enviando para Ollama local: {model_to_use}")
            if not chat_instance.check_connection():
                raise HTTPException(status_code=503, detail="Ollama não está acessível")
            response = chat_instance.send_message(full_prompt, model_to_use)
            print(f"🔍 DEBUG: Resposta do Ollama - {response[:100] if response else 'None'}...")

        # LOGS INTEGRATION - Log de resposta gerada
        if log_manager and response:
            log_manager.add_log(
                "INFO",
                "chat",
                f"Resposta gerada com sucesso",
                {
                    "user": message.message[:50],
                    "assistant": response[:50] + "...",
                    "model": model_to_use,
                    "response_length": len(response)
                }
            )

        # LOGS INTEGRATION - Salvar conversa
        if log_manager and response:
            try:
                log_manager.add_conversation(
                    message.message,
                    response,
                    model_to_use
                )
                print(f"[LOGS] Conversa salva: {model_to_use}")
            except Exception as e:
                print(f"[LOGS] Erro ao salvar (não crítico): {e}")

        # POSTGRES INTEGRATION - Salvar mensagem
        if db_manager and response:
            try:
                # Criar task assíncrona sem bloquear
                import asyncio
                loop = asyncio.get_event_loop()
                loop.create_task(db_manager.save_message(
                    message.message,
                    response,
                    model_to_use
                ))
            except Exception as e:
                print(f"[POSTGRES] Erro ao salvar (não crítico): {e}")

        if response is None:
            raise HTTPException(status_code=500, detail="Erro ao obter resposta do Ollama")

        # FORMATAÇÃO SEGURA - Aplicar apenas se habilitada
        formatted_response = response
        if safe_formatter and ENABLE_FORMATTING:
            try:
                formatted_response = safe_formatter.format_response(response)
                print(f"[FORMATTING] Resposta formatada: {len(formatted_response)} chars")
            except Exception as e:
                print(f"[FORMATTING] Erro na formatação (usando original): {e}")
                formatted_response = response

        # Aplicar formatação markdown
        markdown_response = format_markdown(formatted_response)
        return ChatResponse(response=markdown_response, model=model_to_use)
    
    except HTTPException:
        # Re-raise HTTP exceptions (não alterar comportamento)
        raise
    except Exception as error:
        # LOGS INTEGRATION - Log de erro
        if log_manager:
            log_manager.add_log(
                "ERROR",
                "chat",
                f"Erro ao gerar resposta: {str(error)}",
                {"user": message.message[:50], "model": message.model or "llama2"}
            )
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@app.get("/chat/history")
async def get_chat_history(limit: int = 50):
    """Retorna histórico de conversas do banco de dados"""
    # POSTGRES INTEGRATION - Endpoint de histórico
    if not db_manager:
        return {"error": "Database not configured", "messages": []}
    
    try:
        history = await db_manager.get_history(limit)
        return {
            "success": True,
            "count": len(history),
            "messages": history
        }
    except Exception as e:
        print(f"[POSTGRES] Erro ao buscar histórico: {e}")
        return {
            "error": "Failed to fetch history",
            "details": str(e),
            "messages": []
        }

@app.post("/api/read-file")
async def read_file(request: Request):
    """Ler conteúdo de um arquivo"""
    try:
        data = await request.json()
        file_path = data.get('path', '')
        
        # Segurança: verificar se o path é válido
        if not file_path or '..' in file_path:
            raise HTTPException(status_code=400, detail="Path inválido")
        
        # Ler arquivo
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return {"content": content, "path": file_path}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/create-file")
async def create_file(request: Request):
    """Criar novo arquivo"""
    try:
        data = await request.json()
        file_path = data.get('path', '')
        content = data.get('content', '')
        
        # Criar arquivo
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return {"status": "success", "path": file_path}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/save-file")
async def save_file(request: Request):
    """Salvar conteúdo em um arquivo"""
    try:
        data = await request.json()
        file_path = data.get('path', '')
        content = data.get('content', '')
        
        # Segurança básica
        if not file_path or '..' in file_path:
            raise HTTPException(status_code=400, detail="Path inválido")
        
        # Criar backup antes de salvar
        if os.path.exists(file_path):
            backup_path = f"{file_path}.backup"
            import shutil
            shutil.copy2(file_path, backup_path)
        
        # Salvar arquivo
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return {
            "status": "success",
            "path": file_path,
            "size": len(content),
            "timestamp": "2024-01-01T00:00:00"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/search-files")
async def search_files(request: Request):
    """Buscar arquivos no diretório atual"""
    try:
        data = await request.json()
        query = data.get('query', '').lower()
        
        if not query:
            return []
        
        # Buscar arquivos no diretório atual
        matches = []
        for root, dirs, files in os.walk('.'):
            # Ignorar pastas específicas
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]
            
            for file in files:
                if query in file.lower():
                    relative_path = os.path.relpath(os.path.join(root, file), '.')
                    matches.append(relative_path)
        
        # Limitar resultados
        return sorted(matches[:50])
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# I.3 - Endpoint list directory
@app.post("/api/list-directory")
async def list_directory(request: Request):
    """Listar conteúdo de um diretório"""
    try:
        data = await request.json()
        directory_path = data.get('path', '.')
        
        # Segurança
        if '..' in directory_path:
            raise HTTPException(status_code=400, detail="Path inválido")
        
        # Verificar se existe
        if not os.path.exists(directory_path):
            raise HTTPException(status_code=404, detail="Diretório não encontrado")
        
        items = []
        
        # Listar conteúdo
        for item_name in os.listdir(directory_path):
            item_path = os.path.join(directory_path, item_name)
            
            # Ignorar alguns arquivos/pastas
            if item_name.startswith('.') and item_name not in ['.env', '.gitignore']:
                continue
                
            try:
                stat = os.stat(item_path)
                items.append({
                    'name': item_name,
                    'path': item_path.replace('\\', '/'),
                    'type': 'directory' if os.path.isdir(item_path) else 'file',
                    'size': stat.st_size if os.path.isfile(item_path) else 0,
                    'modified': stat.st_mtime,
                    'extension': Path(item_name).suffix.lower() if os.path.isfile(item_path) else None
                })
            except:
                continue
        
        # Ordenar: pastas primeiro, depois arquivos
        items.sort(key=lambda x: (x['type'] != 'directory', x['name'].lower()))
        
        return {
            'path': directory_path,
            'items': items,
            'count': len(items)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# I.4 - WebSocket file watching
if WATCHDOG_AVAILABLE:
    class FileWatcher(FileSystemEventHandler):
        def __init__(self, websocket_manager):
            self.websocket_manager = websocket_manager
            
        def on_modified(self, event):
            if not event.is_directory:
                asyncio.create_task(
                    self.websocket_manager.broadcast({
                        'type': 'file_modified',
                        'path': event.src_path.replace('\\', '/')
                    })
                )
        
        def on_created(self, event):
            asyncio.create_task(
                self.websocket_manager.broadcast({
                    'type': 'file_created',
                    'path': event.src_path.replace('\\', '/'),
                    'is_directory': event.is_directory
                })
            )
        
        def on_deleted(self, event):
            asyncio.create_task(
                self.websocket_manager.broadcast({
                    'type': 'file_deleted',
                    'path': event.src_path.replace('\\', '/')
                })
            )
        
        def on_moved(self, event):
            asyncio.create_task(
                self.websocket_manager.broadcast({
                    'type': 'file_moved',
                    'old_path': event.src_path.replace('\\', '/'),
                    'new_path': event.dest_path.replace('\\', '/')
                })
            )
else:
    # Fallback quando watchdog não está disponível
    class FileWatcher:
        def __init__(self, websocket_manager):
            self.websocket_manager = websocket_manager

# WebSocket Manager
class WebSocketManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.file_observer = None
        if WATCHDOG_AVAILABLE:
            self.file_watcher = FileWatcher(self)
        
    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        
        # Iniciar file watching se ainda não estiver rodando
        if not self.file_observer and WATCHDOG_AVAILABLE:
            self.start_file_watching()
            
    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        
        # Parar file watching se não houver mais conexões
        if not self.active_connections and self.file_observer:
            self.stop_file_watching()
            
    async def broadcast(self, message: dict):
        for connection in self.active_connections.copy():
            try:
                await connection.send_json(message)
            except:
                # Conexão morta, remover
                if connection in self.active_connections:
                    self.active_connections.remove(connection)
                
    def start_file_watching(self):
        if not WATCHDOG_AVAILABLE:
            return
            
        self.file_observer = Observer()
        self.file_observer.schedule(
            self.file_watcher,
            path=os.getcwd(),
            recursive=True
        )
        self.file_observer.start()
        
    def stop_file_watching(self):
        if self.file_observer:
            self.file_observer.stop()
            self.file_observer.join()
            self.file_observer = None

# Instância global
ws_manager = WebSocketManager()

# WebSocket endpoint
@app.websocket("/ws/file-watch")
async def websocket_file_watch(websocket: WebSocket):
    await ws_manager.connect(websocket)
    try:
        while True:
            # Manter conexão viva
            data = await websocket.receive_text()
            
            # Processar comandos se necessário
            if data == "ping":
                await websocket.send_text("pong")
                
    except WebSocketDisconnect:
        ws_manager.disconnect(websocket)

@app.get("/api/logs")
async def get_logs_endpoint(
    level: Optional[str] = None,
    module: Optional[str] = None,
    model: Optional[str] = None,
    search: Optional[str] = None,
    limit: int = 100
):
    """Retorna logs filtrados"""
    # LOGS INTEGRATION - Endpoint de logs
    if not log_manager:
        return {"error": "Log system not configured", "logs": []}
    
    try:
        logs = log_manager.get_logs(level, module, model, search, limit)
        return {
            "success": True,
            "count": len(logs),
            "logs": logs
        }
    except Exception as e:
        return {"error": str(e), "logs": []}

@app.get("/api/logs/stats")
async def get_logs_stats():
    """Retorna estatísticas dos logs"""
    # LOGS INTEGRATION - Endpoint de estatísticas
    if not log_manager:
        return {"error": "Log system not configured", "stats": {}}

    try:
        stats = log_manager.get_stats()
        return {
            "success": True,
            "stats": stats
        }
    except Exception as e:
        return {"error": str(e), "stats": {}}

# Endpoints adicionais úteis
@app.post("/api/create-folder")
async def create_folder(request: Request):
    """Criar nova pasta"""
    try:
        data = await request.json()
        folder_path = data.get('path', '')
        
        if not folder_path or '..' in folder_path:
            raise HTTPException(status_code=400, detail="Path inválido")
            
        os.makedirs(folder_path, exist_ok=True)
        
        return {
            "status": "success",
            "path": folder_path,
            "created": True
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/delete-item")
async def delete_item(request: Request):
    """Deletar arquivo ou pasta"""
    try:
        data = await request.json()
        item_path = data.get('path', '')
        
        if not item_path or '..' in item_path:
            raise HTTPException(status_code=400, detail="Path inválido")
            
        if os.path.isfile(item_path):
            os.remove(item_path)
        elif os.path.isdir(item_path):
            import shutil
            shutil.rmtree(item_path)
        else:
            raise HTTPException(status_code=404, detail="Item não encontrado")
            
        return {
            "status": "success",
            "path": item_path,
            "deleted": True
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/rename-item")
async def rename_item(request: Request):
    """Renomear arquivo ou pasta"""
    try:
        data = await request.json()
        old_path = data.get('old_path', '')
        new_path = data.get('new_path', '')
        
        if not old_path or not new_path or '..' in old_path or '..' in new_path:
            raise HTTPException(status_code=400, detail="Path inválido")
            
        os.rename(old_path, new_path)
        
        return {
            "status": "success",
            "old_path": old_path,
            "new_path": new_path
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/ai/reconnect")
async def reconnect_ais():
    """Reconecta todas as AIs externas"""
    try:
        # Reinicializar o sistema unificado de IA
        global unified_ai
        from ai_providers.unified_ai import UnifiedAI
        unified_ai = UnifiedAI()

        # Verificar conexões
        available_models = unified_ai.get_available_external_models()

        # Log da reconexão
        if log_manager:
            log_manager.add_log(
                "INFO",
                "system",
                f"AIs reconectadas: {', '.join(available_models)}",
                {"reconnected_models": available_models}
            )

        return {
            "success": True,
            "message": "AIs reconectadas com sucesso",
            "available_models": available_models,
            "count": len(available_models)
        }
    except Exception as e:
        if log_manager:
            log_manager.add_log(
                "ERROR",
                "system",
                f"Erro ao reconectar AIs: {str(e)}"
            )
        return {
            "success": False,
            "error": str(e),
            "available_models": []
        }

@app.post("/api/logs/clear")
async def clear_logs():
    """Limpa todos os logs"""
    # LOGS INTEGRATION - Endpoint para limpar logs
    if not log_manager:
        return {"error": "Log system not configured"}
    
    try:
        log_manager.clear_logs()
        return {"success": True, "message": "Logs cleared"}
    except Exception as e:
        return {"error": str(e)}

@app.post("/api/conversations/clear")
async def clear_conversations():
    """LIMPA TODAS AS CONVERSAS - OPERAÇÃO IRREVERSÍVEL"""
    if not log_manager:
        return {"error": "Log system not configured"}
    
    try:
        # Obter contagem antes de limpar
        count_before = log_manager.get_conversation_count()
        
        # Limpar tudo
        log_manager.clear_conversations()
        
        return {
            "success": True, 
            "message": f"✅ {count_before} conversas removidas - contexto resetado!",
            "conversations_cleared": count_before
        }
    except Exception as e:
        return {"error": str(e)}

@app.get("/api/conversations/count")
async def get_conversation_count():
    """Retorna número de conversas salvas"""
    if not log_manager:
        return {"error": "Log system not configured", "count": 0}
    
    try:
        count = log_manager.get_conversation_count()
        return {"success": True, "count": count}
    except Exception as e:
        return {"error": str(e), "count": 0}

@app.get("/api/conversations/by-date")
async def get_conversations_by_date():
    """Retorna conversas agrupadas por data"""
    if not log_manager:
        return {"error": "Log system not configured", "dates": {}}
    
    try:
        date_counts = log_manager.get_conversations_by_date()
        return {"success": True, "dates": date_counts}
    except Exception as e:
        return {"error": str(e), "dates": {}}

@app.post("/api/conversations/clear-date")
async def clear_conversations_by_date(request: dict):
    """Limpa conversas de uma data específica"""
    if not log_manager:
        return {"error": "Log system not configured"}
    
    target_date = request.get("date")
    if not target_date:
        return {"error": "Data não fornecida"}
    
    try:
        removed_count = log_manager.clear_conversations_by_date(target_date)
        return {
            "success": True,
            "message": f"✅ {removed_count} conversas do dia {target_date} removidas!",
            "conversations_cleared": removed_count,
            "date": target_date
        }
    except Exception as e:
        return {"error": str(e)}

@app.get("/api/conversations")
async def get_conversations(limit: int = 20):
    """Retorna conversas completas com análises em cascata"""
    if not log_manager:
        return {"error": "Log system not configured", "conversations": []}
    
    try:
        conversations = log_manager.get_full_conversations(limit)
        return {
            "success": True,
            "count": len(conversations),
            "conversations": conversations
        }
    except Exception as e:
        return {"error": str(e), "conversations": []}

@app.post("/api/analyze-cascade")
async def analyze_cascade(request: dict):
    """Inicia análise em cascata de uma conversa"""
    if not chat_instance:
        return {"error": "Chat system not available"}
    
    try:
        from cascade.cascade_analyzer import CascadeAnalyzer
        from datetime import datetime
        
        analyzer = CascadeAnalyzer(chat_instance.base_url)
        result = await analyzer.run_cascade_analysis(
            request.get("user_message"),
            request.get("assistant_message"),
            request.get("model")
        )
        
        return {
            "success": True,
            "result": result
        }
    except Exception as e:
        return {"error": str(e)}

@app.post("/api/multi-chat", response_model=MultiChatResponse)
async def multi_chat_endpoint(request: MultiChatMessage):
    """Endpoint para múltiplas IAs conversarem entre si"""
    try:
        if not chat_instance.check_connection():
            raise HTTPException(status_code=503, detail="Ollama não está acessível")
        
        if not request.message.strip():
            raise HTTPException(status_code=400, detail="Mensagem inicial não pode estar vazia")
        
        if len(request.models) < 2:
            raise HTTPException(status_code=400, detail="Precisa de pelo menos 2 modelos para conversar")
        
        conversation = []
        current_message = request.message
        
        # Log inicial
        if log_manager:
            log_manager.add_log(
                "INFO",
                "multi-chat",
                f"Iniciando conversa multi-IA com {len(request.models)} modelos",
                {"models": request.models, "rounds": request.rounds}
            )
        
        # Rodadas de conversa
        for round_num in range(request.rounds):
            round_responses = []
            
            # Cada modelo responde baseado na mensagem atual + histórico
            for i, model in enumerate(request.models):
                # Construir contexto para o modelo
                context = f"Mensagem inicial: {request.message}\n\n"
                
                # Adicionar histórico da conversa
                if conversation:
                    context += "Histórico da conversa:\n"
                    for entry in conversation:
                        for resp in entry['responses']:
                            context += f"{resp['model']}: {resp['response']}\n"
                    context += "\n"
                
                # Adicionar mensagem atual
                if round_num == 0:
                    prompt = current_message
                else:
                    # Após a primeira rodada, responder ao último modelo que falou
                    last_speaker = conversation[-1]['responses'][-1]
                    prompt = f"Responda a {last_speaker['model']} que disse: {last_speaker['response']}"
                
                # Enviar para o modelo
                try:
                    response = chat_instance.send_message(context + prompt, model)
                    
                    round_responses.append({
                        "model": model,
                        "response": response,
                        "timestamp": datetime.now().isoformat()
                    })
                    
                    # Log de cada resposta
                    if log_manager:
                        log_manager.add_log(
                            "INFO",
                            "multi-chat",
                            f"Resposta de {model} na rodada {round_num + 1}",
                            {
                                "model": model,
                                "round": round_num + 1,
                                "response_length": len(response)
                            }
                        )
                    
                    # Atualizar mensagem para o próximo modelo
                    current_message = response
                    
                except Exception as e:
                    round_responses.append({
                        "model": model,
                        "response": f"Erro: {str(e)}",
                        "timestamp": datetime.now().isoformat(),
                        "error": True
                    })
            
            # Adicionar rodada à conversa
            conversation.append({
                "round": round_num + 1,
                "responses": round_responses
            })
        
        # Salvar conversa completa no banco se disponível
        if db_manager:
            try:
                import asyncio
                import json
                loop = asyncio.get_event_loop()
                # Por enquanto, salvar como mensagem normal com metadados
                conversation_summary = f"Multi-Chat ({len(request.models)} modelos, {request.rounds} rodadas)"
                full_text = json.dumps({
                    "type": "multi-chat",
                    "initial_message": request.message,
                    "models": request.models,
                    "conversation": conversation
                }, ensure_ascii=False)
                
                loop.create_task(db_manager.save_message(
                    conversation_summary,
                    full_text,
                    "multi-chat"
                ))
            except Exception as e:
                print(f"[MULTI-CHAT] Erro ao salvar conversa: {e}")
        
        return MultiChatResponse(
            conversation=conversation,
            models=request.models
        )
        
    except HTTPException:
        raise
    except Exception as error:
        if log_manager:
            log_manager.add_log(
                "ERROR",
                "multi-chat",
                f"Erro na conversa multi-IA: {str(error)}",
                {"models": request.models}
            )
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@app.get("/logs", response_class=HTMLResponse)
async def get_logs_interface():
    """Interface web para visualizar logs"""
    html_content = '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Lilith System Logs</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Inter', sans-serif;
                background: #0a0a0a;
                color: #ffffff;
                padding: 20px;
                height: 100vh;
                overflow: hidden;
                display: flex;
                flex-direction: column;
            }

            .header {
                background: #171717;
                padding: 20px;
                border-radius: 12px;
                margin-bottom: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border: 1px solid #333;
            }

            .header h1 {
                font-size: 24px;
                color: #ff0000;
                text-shadow: 0 0 10px rgba(255, 0, 0, 0.5);
            }

            .controls {
                display: flex;
                gap: 10px;
            }

            .btn {
                background: #202020;
                border: 1px solid #333;
                color: #fff;
                padding: 8px 16px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                transition: all 0.3s;
            }

            .btn:hover {
                background: #2a2a2a;
                border-color: #555;
            }

            .btn-danger {
                border-color: #ff0000;
                color: #ff0000;
            }

            .btn-danger:hover {
                background: rgba(255, 0, 0, 0.1);
                box-shadow: 0 0 10px rgba(255, 0, 0, 0.3);
            }

            .filters {
                background: #171717;
                padding: 15px;
                border-radius: 12px;
                margin-bottom: 20px;
                display: flex;
                gap: 15px;
                align-items: center;
                border: 1px solid #333;
            }

            select, input[type="text"] {
                background: #202020;
                border: 1px solid #333;
                color: #fff;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 14px;
                outline: none;
            }

            select:focus, input[type="text"]:focus {
                border-color: #555;
            }

            .logs-container {
                background: #171717;
                border: 1px solid #333;
                border-radius: 12px;
                flex: 1;
                overflow: hidden;
                display: flex;
                flex-direction: column;
            }

            .logs-table {
                width: 100%;
                flex: 1;
                overflow-y: auto;
            }

            table {
                width: 100%;
                border-collapse: collapse;
            }

            th {
                background: #202020;
                padding: 12px;
                text-align: left;
                font-weight: 600;
                border-bottom: 2px solid #333;
                position: sticky;
                top: 0;
                z-index: 10;
            }

            td {
                padding: 10px 12px;
                border-bottom: 1px solid #252525;
                font-size: 13px;
            }

            tr:hover {
                background: rgba(255, 255, 255, 0.02);
            }

            .level-INFO { color: #4CAF50; }
            .level-WARNING { color: #FFC107; }
            .level-ERROR { color: #f44336; }
            .level-DEBUG { color: #2196F3; }

            .model-wizard { color: #9C27B0; }
            .model-llama { color: #2196F3; }
            .model-mistral { color: #4CAF50; }

            .stats {
                background: #202020;
                padding: 15px;
                border-top: 1px solid #333;
                display: flex;
                gap: 30px;
                font-size: 14px;
            }

            .stat-item {
                display: flex;
                align-items: center;
                gap: 5px;
            }

            .loading {
                text-align: center;
                padding: 50px;
                color: #666;
            }

            .message-preview {
                max-width: 400px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .timestamp {
                color: #666;
                font-size: 12px;
            }

            ::-webkit-scrollbar {
                width: 10px;
            }

            ::-webkit-scrollbar-track {
                background: #0a0a0a;
            }

            ::-webkit-scrollbar-thumb {
                background: #333;
                border-radius: 5px;
            }

            ::-webkit-scrollbar-thumb:hover {
                background: #444;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1 style="color: #202020; font-weight: 300;">System Logs</h1>
            <div class="controls">
                <button class="btn" onclick="refreshLogs()">↻ Refresh</button>
                <button class="btn btn-danger" onclick="clearLogs()">Clear Logs</button>
                <button class="btn" onclick="downloadLogs()">↓ Download</button>
            </div>
        </div>

        <div class="filters">
            <select id="levelFilter" onchange="refreshLogs()">
                <option value="">All Levels</option>
                <option value="INFO">INFO</option>
                <option value="WARNING">WARNING</option>
                <option value="ERROR">ERROR</option>
                <option value="DEBUG">DEBUG</option>
            </select>

            <select id="moduleFilter" onchange="refreshLogs()">
                <option value="">All Modules</option>
                <option value="chat">Chat</option>
                <option value="system">System</option>
                <option value="postgres">PostgreSQL</option>
                <option value="api">API</option>
            </select>

            <select id="modelFilter" onchange="refreshLogs()">
                <option value="">All Models</option>
            </select>

            <input type="text" id="searchFilter" placeholder="Search logs..." onkeyup="refreshLogs()">
            
            <label style="color: #666; font-size: 12px;">
                <input type="checkbox" id="autoRefresh" checked> Auto-refresh
            </label>
        </div>

        <div class="logs-container">
            <div class="logs-table">
                <table>
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>Level</th>
                            <th>Module</th>
                            <th>Model</th>
                            <th>Message</th>
                        </tr>
                    </thead>
                    <tbody id="logsBody">
                        <tr>
                            <td colspan="5" class="loading">Loading logs...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="stats" id="statsBar">
                <div class="stat-item">📊 Total: <span id="totalLogs">0</span></div>
            </div>
        </div>

        <script>
            let refreshInterval;
            let knownModels = new Set();

            async function loadLogs() {
                const level = document.getElementById('levelFilter').value;
                const module = document.getElementById('moduleFilter').value;
                const model = document.getElementById('modelFilter').value;
                const search = document.getElementById('searchFilter').value;

                try {
                    const params = new URLSearchParams({
                        limit: 200
                    });
                    if (level) params.append('level', level);
                    if (module) params.append('module', module);
                    if (model) params.append('model', model);
                    if (search) params.append('search', search);

                    const response = await fetch(`/api/logs?${params}`);
                    const data = await response.json();

                    if (data.success) {
                        displayLogs(data.logs);
                        updateStats();
                    }
                } catch (error) {
                    console.error('Error loading logs:', error);
                }
            }

            function displayLogs(logs) {
                const tbody = document.getElementById('logsBody');
                
                if (logs.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="5" class="loading">No logs found</td></tr>';
                    return;
                }

                tbody.innerHTML = logs.map(log => {
                    const model = log.details?.model || '-';
                    const modelClass = model.includes('wizard') ? 'model-wizard' : 
                                     model.includes('llama') ? 'model-llama' : 
                                     model.includes('mistral') ? 'model-mistral' : '';
                    
                    // Adicionar modelo à lista
                    if (model !== '-') {
                        knownModels.add(model);
                    }

                    return `
                        <tr>
                            <td class="timestamp">${log.timestamp}</td>
                            <td class="level-${log.level}">${log.level}</td>
                            <td>${log.module}</td>
                            <td class="${modelClass}">${model}</td>
                            <td class="message-preview" title="${escapeHtml(log.message)}">${escapeHtml(log.message)}</td>
                        </tr>
                    `;
                }).join('');

                // Atualizar select de modelos
                updateModelFilter();
            }

            function updateModelFilter() {
                const select = document.getElementById('modelFilter');
                const currentValue = select.value;
                
                select.innerHTML = '<option value="">All Models</option>';
                [...knownModels].sort().forEach(model => {
                    select.innerHTML += `<option value="${model}">${model}</option>`;
                });
                
                select.value = currentValue;
            }

            async function updateStats() {
                try {
                    const response = await fetch('/api/logs/stats');
                    const data = await response.json();

                    if (data.success) {
                        const stats = data.stats;
                        let statsHtml = `<div class="stat-item">📊 Total: <span>${stats.total}</span></div>`;
                        
                        // Por nível
                        Object.entries(stats.by_level || {}).forEach(([level, count]) => {
                            const icon = level === 'ERROR' ? '❌' : level === 'WARNING' ? '⚠️' : '✅';
                            statsHtml += `<div class="stat-item">${icon} ${level}: <span>${count}</span></div>`;
                        });

                        // Por modelo
                        if (Object.keys(stats.by_model || {}).length > 0) {
                            statsHtml += '<div style="margin-left: 20px; color: #666;">|</div>';
                            Object.entries(stats.by_model || {}).forEach(([model, data]) => {
                                const shortModel = model.split(':')[0];
                                statsHtml += `<div class="stat-item">🤖 ${shortModel}: <span>${data.total}</span></div>`;
                            });
                        }

                        document.getElementById('statsBar').innerHTML = statsHtml;
                    }
                } catch (error) {
                    console.error('Error loading stats:', error);
                }
            }

            function escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            async function clearLogs() {
                if (!confirm('Clear all logs? This cannot be undone.')) return;

                try {
                    const response = await fetch('/api/logs/clear', { method: 'POST' });
                    const data = await response.json();
                    
                    if (data.success) {
                        refreshLogs();
                    }
                } catch (error) {
                    console.error('Error clearing logs:', error);
                }
            }

            function downloadLogs() {
                // TODO: Implementar download
                alert('Download feature coming soon!');
            }

            function refreshLogs() {
                loadLogs();
            }

            function startAutoRefresh() {
                const checkbox = document.getElementById('autoRefresh');
                
                if (refreshInterval) {
                    clearInterval(refreshInterval);
                }

                if (checkbox.checked) {
                    refreshInterval = setInterval(refreshLogs, 2000);
                }
            }

            // Event listeners
            document.getElementById('autoRefresh').addEventListener('change', startAutoRefresh);

            // Inicializar
            refreshLogs();
            startAutoRefresh();
        </script>
    </body>
    </html>
    '''
    return HTMLResponse(content=html_content)

# Classes para geração de imagens
class ImagePrompt(BaseModel):
    prompt: str
    negative_prompt: Optional[str] = ""
    width: Optional[int] = 768
    height: Optional[int] = 768
    steps: Optional[int] = 40
    cfg_scale: Optional[float] = 7.0
    
@app.post("/generate-image")
async def generate_image(request: ImagePrompt):
    """Endpoint para gerar imagens via Stable Diffusion"""
    try:
        import requests
        
        # URL do Stable Diffusion
        SD_URL = "http://localhost:7860"
        
        # Verificar se SD está rodando
        try:
            health_check = requests.get(f"{SD_URL}/sdapi/v1/options", timeout=5)
            if health_check.status_code != 200:
                return {
                    "success": False, 
                    "error": "❌ Stable Diffusion não está respondendo. Inicie o SD primeiro: execute webui-user.bat na pasta stable-diffusion-webui"
                }
        except requests.exceptions.ConnectionError:
            return {
                "success": False,
                "error": "❌ Stable Diffusion não está rodando. Execute: python lilith.py e escolha opção 3 (Iniciar Tudo)"
            }
        except requests.exceptions.Timeout:
            return {
                "success": False,
                "error": "⏱️ Stable Diffusion está muito lento para responder. Aguarde alguns segundos e tente novamente."
            }
        
        # Payload com configurações premium
        payload = {
            "prompt": request.prompt,
            "negative_prompt": request.negative_prompt,
            "width": request.width,
            "height": request.height,
            "steps": request.steps,
            "cfg_scale": request.cfg_scale,
            "sampler_name": "DPM++ 3M SDE",
            "scheduler": "normal",
            "enable_hr": True,
            "hr_upscaler": "4x-UltraSharp",
            "hr_second_pass_steps": 0,
            "denoising_strength": 0.4,
            "hr_scale": 1,
            "batch_size": 1,
            "seed": -1
        }
        
        print(f"[SD] Gerando imagem: {request.prompt[:50]}...")
        
        # Chama API do Stable Diffusion
        response = requests.post(
            f"{SD_URL}/sdapi/v1/txt2img",
            json=payload,
            timeout=900
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"[SD] ✅ Imagem gerada com sucesso")
            return {"success": True, "images": result["images"]}
        else:
            error_msg = f"SD API Error: {response.status_code}"
            try:
                error_detail = response.json()
                if "error" in error_detail:
                    error_msg += f" - {error_detail['error']}"
            except:
                pass
            return {"success": False, "error": error_msg}
            
    except Exception as e:
        return {"success": False, "error": f"Erro interno: {str(e)}"}

@app.get("/sd-status")
async def check_sd_status():
    """Verifica se Stable Diffusion está rodando"""
    try:
        import requests
        response = requests.get("http://localhost:7860/sdapi/v1/options", timeout=3)
        if response.status_code == 200:
            return {
                "status": "online",
                "message": "✅ Stable Diffusion está rodando e pronto para gerar imagens!"
            }
        else:
            return {
                "status": "error", 
                "message": f"❌ SD respondeu com erro: {response.status_code}"
            }
    except requests.exceptions.ConnectionError:
        return {
            "status": "offline",
            "message": "❌ Stable Diffusion não está rodando. Execute start_stable_diffusion.bat para iniciar."
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"❌ Erro ao verificar SD: {str(e)}"
        }


def start_painel():
    """Função para iniciar o painel enterprise"""
    print("=" * 60)
    print("🚀 INICIANDO LILITH PAINEL - DESIGN EXATO")
    print("=" * 60)
    print("🖥️  Interface: http://localhost:8000")
    
    # Testar conexão com Ollama
    print("🔍 Testando conexão com Ollama...")
    if chat_instance.check_connection():
        print(f"   ✅ Ollama conectado em {chat_instance.base_url}")
        models = chat_instance.list_models()
        print(f"   📋 Modelos disponíveis: {models}")
    else:
        print(f"   ❌ Ollama não conectado em {chat_instance.base_url}")
        print("   💡 Certifique-se de que o Ollama está rodando")
    
    # STABLE DIFFUSION STATUS
    print("🎨 Verificando Stable Diffusion...")
    import requests
    try:
        response = requests.get("http://localhost:7860/sdapi/v1/options", timeout=3)
        if response.status_code == 200:
            print("   ✅ Stable Diffusion está online!")
        else:
            print("   ⚠️ Stable Diffusion responde mas com erro")
    except:
        print("   ❌ Stable Diffusion offline - use 'python lilith.py' opção 3 para iniciar tudo")
    
    # POSTGRES INTEGRATION - Inicialização
    if db_manager:
        import asyncio
        try:
            asyncio.run(db_manager.connect())
            print("[POSTGRES] Conexão iniciada (se PostgreSQL estiver rodando)")
        except:
            print("[POSTGRES] PostgreSQL não está rodando - sistema continua sem persistência")
    
    print("=" * 60)

    uvicorn.run(app, host="0.0.0.0", port=8000)

if __name__ == "__main__":
    start_painel()