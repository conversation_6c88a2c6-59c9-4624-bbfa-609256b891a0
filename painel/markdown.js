/*
 * markdown.js - Funcionalidades interativas para markdown
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('📝 Markdown JavaScript carregado');
    
    // Adicionar botões de cópia aos blocos de código
    addCopyButtonsToCodeBlocks();
    
    // Observar novos elementos markdown adicionados dinamicamente
    observeMarkdownContent();
});

function addCopyButtonsToCodeBlocks() {
    const codeBlocks = document.querySelectorAll('.md-code-block');
    
    codeBlocks.forEach(block => {
        // Verificar se já tem botão
        if (block.querySelector('.copy-btn')) return;
        
        // Criar botão de cópia
        const copyBtn = document.createElement('button');
        copyBtn.className = 'copy-btn';
        copyBtn.innerHTML = '📋 Copy';
        copyBtn.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            background: #333;
            border: 1px solid #555;
            color: #fff;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            opacity: 0;
            transition: all 0.3s ease;
            font-family: 'Inter', sans-serif;
        `;
        
        // Adicionar eventos
        block.style.position = 'relative';
        block.appendChild(copyBtn);
        
        // Mostrar/esconder botão
        block.addEventListener('mouseenter', () => {
            copyBtn.style.opacity = '1';
        });
        
        block.addEventListener('mouseleave', () => {
            copyBtn.style.opacity = '0';
        });
        
        // Funcionalidade de cópia
        copyBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const code = block.querySelector('.md-code');
            if (code) {
                const text = code.textContent || code.innerText;
                
                // Usar API moderna de clipboard
                if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(text).then(() => {
                        showCopySuccess(copyBtn);
                    }).catch(() => {
                        fallbackCopy(text, copyBtn);
                    });
                } else {
                    fallbackCopy(text, copyBtn);
                }
            }
        });
    });
}

function showCopySuccess(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '✅ Copied!';
    button.style.background = '#4ecdc4';
    button.style.borderColor = '#4ecdc4';
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.style.background = '#333';
        button.style.borderColor = '#555';
    }, 2000);
}

function fallbackCopy(text, button) {
    // Fallback para navegadores mais antigos
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        showCopySuccess(button);
    } catch (err) {
        console.error('Erro ao copiar:', err);
        button.innerHTML = '❌ Error';
        setTimeout(() => {
            button.innerHTML = '📋 Copy';
        }, 2000);
    }
    
    document.body.removeChild(textArea);
}

function observeMarkdownContent() {
    // Observer para detectar novos elementos markdown
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    // Verificar se o novo elemento contém código
                    const codeBlocks = node.querySelectorAll ? node.querySelectorAll('.md-code-block') : [];
                    if (codeBlocks.length > 0) {
                        addCopyButtonsToCodeBlocks();
                    }
                    
                    // Verificar se o próprio elemento é um bloco de código
                    if (node.classList && node.classList.contains('md-code-block')) {
                        addCopyButtonsToCodeBlocks();
                    }
                }
            });
        });
    });
    
    // Observar mudanças no chat
    const chatContainer = document.getElementById('messagesInner') || document.body;
    observer.observe(chatContainer, {
        childList: true,
        subtree: true
    });
}

// Função para processar markdown em tempo real (se necessário)
function processMarkdownElements() {
    // Processar links externos
    const links = document.querySelectorAll('.md-link');
    links.forEach(link => {
        if (link.href && !link.href.startsWith(window.location.origin)) {
            link.setAttribute('target', '_blank');
            link.setAttribute('rel', 'noopener noreferrer');
        }
    });
    
    // Adicionar funcionalidades extras se necessário
    addCopyButtonsToCodeBlocks();
}

// Função principal para formatar markdown (espelho da função Python)
function formatMarkdown(text) {
    if (!text) return "";
    
    // Escapar HTML primeiro
    text = text.replace(/&/g, '&amp;')
               .replace(/</g, '&lt;')
               .replace(/>/g, '&gt;')
               .replace(/"/g, '&quot;')
               .replace(/'/g, '&#x27;');
    
    // Processar elementos markdown
    text = processCodeBlocks(text);
    text = processHeaders(text);
    text = processBoldItalic(text);
    text = processInlineCode(text);
    text = processLinks(text);
    text = processLists(text);
    text = processBlockquotes(text);
    text = processParagraphs(text);
    
    return `<div class="md-content">${text}</div>`;
}

function processHeaders(text) {
    return text.replace(/^(#{1,6})\s+(.+)$/gm, function(match, hashes, content) {
        const level = hashes.length;
        return `<h${level} class="md-h${level}">${content}</h${level}>`;
    });
}

function processCodeBlocks(text) {
    return text.replace(/```(\w+)?\n([\s\S]*?)```/g, function(match, lang, code) {
        lang = lang || 'text';
        // Desfazer escape para o código
        code = code.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&');
        return `<pre class="md-code-block"><code class="md-code md-lang-${lang}">${code}</code></pre>`;
    });
}

function processBoldItalic(text) {
    // Bold + Italic
    text = text.replace(/\*\*\*(.+?)\*\*\*/g, '<strong class="md-bold-italic"><em>$1</em></strong>');
    // Bold
    text = text.replace(/\*\*(.+?)\*\*/g, '<strong class="md-bold">$1</strong>');
    // Italic
    text = text.replace(/\*(.+?)\*/g, '<em class="md-italic">$1</em>');
    return text;
}

function processInlineCode(text) {
    return text.replace(/`([^`]+)`/g, '<code class="md-inline-code">$1</code>');
}

function processLinks(text) {
    return text.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="md-link">$1</a>');
}

function processLists(text) {
    // Unordered lists
    text = text.replace(/^(\s*)[*+-]\s+(.+)$/gm, '<div class="md-list-item"><span class="md-bullet">•</span><span class="md-list-content">$2</span></div>');
    // Ordered lists
    text = text.replace(/^(\s*)(\d+)\.\s+(.+)$/gm, '<div class="md-list-item"><span class="md-number">$2.</span><span class="md-list-content">$3</span></div>');
    return text;
}

function processBlockquotes(text) {
    return text.replace(/^>\s+(.+)$/gm, '<blockquote class="md-blockquote">$1</blockquote>');
}

function processParagraphs(text) {
    const lines = text.split('\n');
    const result = [];
    
    for (let line of lines) {
        if (line.trim()) {
            if (!line.trim().startsWith('<')) {
                result.push(`<p class="md-paragraph">${line}</p>`);
            } else {
                result.push(line);
            }
        } else {
            result.push('');
        }
    }
    
    return result.join('\n');
}

// Exportar função para uso global
window.markdownJS = {
    processElements: processMarkdownElements,
    addCopyButtons: addCopyButtonsToCodeBlocks
};

// Exportar formatMarkdown globalmente
window.formatMarkdown = formatMarkdown;