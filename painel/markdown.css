/* 
 * markdown.css - Sistema de estilos markdown com paleta #171717
 */

/* Container principal */
.md-content {
    color: #ffffff;
    line-height: 1.6;
    font-family: 'Inter', sans-serif;
}

/* Headers */
.md-h1 {
    color: #ff0000;
    font-size: 2rem;
    font-weight: 600;
    margin: 1.5rem 0 1rem 0;
    text-shadow: 0 0 10px #ff0000;
}

.md-h2 {
    color: #ff4444;
    font-size: 1.5rem;
    font-weight: 500;
    margin: 1.25rem 0 0.75rem 0;
    text-shadow: 0 0 8px #ff4444;
}

.md-h3 {
    color: #ff6666;
    font-size: 1.25rem;
    font-weight: 500;
    margin: 1rem 0 0.5rem 0;
}

.md-h4, .md-h5, .md-h6 {
    color: #ff8888;
    font-size: 1rem;
    font-weight: 500;
    margin: 0.75rem 0 0.5rem 0;
}

/* Parágrafos */
.md-paragraph {
    margin: 0.75rem 0;
    color: #ffffff;
}

/* Texto formatado */
.md-bold {
    font-weight: 600;
    color: #ffffff;
}

.md-italic {
    font-style: italic;
    color: #cccccc;
}

.md-bold-italic {
    font-weight: 600;
    font-style: italic;
    color: #ffffff;
}

/* Código */
.md-code-block {
    background: #171717;
    border: 1px solid #333;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    overflow-x: auto;
    position: relative;
}

.md-code {
    color: #ffffff;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
}

.md-inline-code {
    background: #171717;
    border: 1px solid #333;
    color: #ff6666;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 0.9rem;
}

/* Syntax highlighting */
.md-lang-python .md-keyword { color: #ff6b6b; }
.md-lang-python .md-builtin { color: #ff0000; }
.md-lang-python .md-string { color: #95e1d3; }
.md-lang-python .md-comment { color: #666; }

.md-lang-javascript .md-keyword { color: #ff6b6b; }
.md-lang-css .md-selector { color: #ff0000; }
.md-lang-css .md-property { color: #95e1d3; }

/* Links */
.md-link {
    color: #ff0000;
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: all 0.3s ease;
}

.md-link:hover {
    color: #ffffff;
    border-bottom-color: #ff0000;
    text-shadow: 0 0 8px #ff0000;
}

/* Listas */
.md-list-item {
    display: flex;
    align-items: flex-start;
    margin: 0.5rem 0;
    padding-left: 0;
}

.md-bullet {
    color: #ff6666;
    margin-right: 0.75rem;
    font-weight: bold;
    flex-shrink: 0;
    text-shadow: 0 0 4px #ff6666;
}

.md-number {
    color: #ff0000;
    margin-right: 0.75rem;
    font-weight: bold;
    flex-shrink: 0;
    text-shadow: 0 0 4px #ff0000;
}

.md-list-content {
    flex: 1;
    color: #ffffff;
}

/* Citações */
.md-blockquote {
    border-left: 4px solid #ff6666;
    background: rgba(255, 102, 102, 0.1);
    padding: 1rem 1.5rem;
    margin: 1rem 0;
    font-style: italic;
    color: #cccccc;
    box-shadow: 0 0 10px rgba(255, 102, 102, 0.2);
}

/* Tabelas */
.md-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
    background: #171717;
    border: 1px solid #333;
    border-radius: 8px;
    overflow: hidden;
}

.md-th {
    background: #2a2a2a;
    color: #ff6666;
    padding: 0.75rem;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid #ff6666;
}

.md-td {
    padding: 0.75rem;
    border-bottom: 1px solid #333;
    color: #ffffff;
}

.md-table tr:last-child .md-td {
    border-bottom: none;
}

.md-table tr:nth-child(even) {
    background: rgba(255, 255, 255, 0.02);
}

/* Linha horizontal */
.md-hr {
    border: none;
    height: 2px;
    background: linear-gradient(90deg, transparent, #ff6666, transparent);
    margin: 2rem 0;
    box-shadow: 0 0 10px rgba(255, 102, 102, 0.3);
}

/* Responsivo */
@media (max-width: 768px) {
    .md-h1 { font-size: 1.75rem; }
    .md-h2 { font-size: 1.5rem; }
    .md-h3 { font-size: 1.25rem; }
    
    .md-code-block {
        padding: 0.75rem;
        font-size: 0.8rem;
    }
    
    .md-table {
        font-size: 0.9rem;
    }
    
    .md-th, .md-td {
        padding: 0.5rem;
    }
}

/* Animações sutis */
.md-code-block {
    transition: box-shadow 0.3s ease;
}

.md-code-block:hover {
    box-shadow: 0 0 20px rgba(255, 102, 102, 0.1);
}

.md-table:hover {
    box-shadow: 0 0 20px rgba(255, 102, 102, 0.1);
}