#!/usr/bin/env python3
"""
unified_ai.py - Sistema unificado para múltiplas APIs de IA

Suporta:
- OpenAI (GPT-4, GPT-4o)
- Anthropic (<PERSON>)
- Google (Gemini)
- DeepSeek (V3)
- <PERSON>llama (modelos locais)
"""

import os
import asyncio
import httpx
import json
from typing import Optional, Dict, List
from datetime import datetime


class UnifiedAI:
    """
    Gerenciador unificado para múltiplas APIs de IA.
    Permite usar todas as APIs com a mesma interface.
    """
    
    def __init__(self):
        """Inicializa o gerenciador com todas as APIs configuradas."""
        # Carregar variáveis de ambiente
        from dotenv import load_dotenv
        load_dotenv()
        
        # Configurações das APIs
        self.apis = {
            # OpenAI
            "gpt-4": {
                "provider": "openai",
                "api_key": os.getenv("OPENAI_API_KEY"),
                "base_url": "https://api.openai.com/v1/chat/completions",
                "model": "gpt-4",
                "headers": lambda key: {
                    "Authorization": f"Bearer {key}",
                    "Content-Type": "application/json"
                }
            },
            "gpt-4o": {
                "provider": "openai", 
                "api_key": os.getenv("OPENAI_API_KEY"),
                "base_url": "https://api.openai.com/v1/chat/completions",
                "model": "gpt-4o",
                "headers": lambda key: {
                    "Authorization": f"Bearer {key}",
                    "Content-Type": "application/json"
                }
            },
            
            # Anthropic Claude
            "claude-sonnet": {
                "provider": "anthropic",
                "api_key": os.getenv("ANTHROPIC_API_KEY"),
                "base_url": "https://api.anthropic.com/v1/messages",
                "model": "claude-3-5-sonnet-20241022",
                "headers": lambda key: {
                    "x-api-key": key,
                    "anthropic-version": "2023-06-01",
                    "Content-Type": "application/json"
                }
            },
            "claude-opus": {
                "provider": "anthropic",
                "api_key": os.getenv("ANTHROPIC_API_KEY"),
                "base_url": "https://api.anthropic.com/v1/messages",
                "model": "claude-3-opus-20240229",
                "headers": lambda key: {
                    "x-api-key": key,
                    "anthropic-version": "2023-06-01",
                    "Content-Type": "application/json"
                }
            },
            
            # Google Gemini
            "gemini-pro": {
                "provider": "google",
                "api_key": os.getenv("GOOGLE_API_KEY"),
                "base_url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent",
                "model": "gemini-1.5-flash",
                "headers": lambda key: {
                    "Content-Type": "application/json"
                }
            },
            
            # DeepSeek V3
            "deepseek-v3": {
                "provider": "deepseek",
                "api_key": os.getenv("DEEPSEEK_API_KEY"),
                "base_url": "https://api.deepseek.com/v1/chat/completions",
                "model": "deepseek-chat",
                "headers": lambda key: {
                    "Authorization": f"Bearer {key}",
                    "Content-Type": "application/json"
                }
            }
        }
        
        # Verificar quais APIs estão disponíveis
        self.available_models = []
        for model_name, config in self.apis.items():
            if config["api_key"]:
                self.available_models.append(model_name)
                print(f"[AI_PROVIDERS] {model_name} configurado")

                # Debug específico para Gemini
                if model_name == "gemini-pro":
                    print(f"[AI_PROVIDERS] Gemini URL: {config['base_url']}")
                    print(f"[AI_PROVIDERS] Gemini Key: {config['api_key'][:20]}...")
            else:
                print(f"[AI_PROVIDERS] {model_name} - chave não encontrada")
    
    def is_external_model(self, model: str) -> bool:
        """Verifica se o modelo é uma API externa."""
        return model in self.apis
    
    def get_available_external_models(self) -> List[str]:
        """Retorna lista de modelos externos disponíveis."""
        return self.available_models
    
    async def send_message(self, message: str, model: str) -> str:
        """
        Envia mensagem para qualquer API de IA de forma unificada.
        
        Args:
            message (str): Mensagem/prompt para enviar
            model (str): Nome do modelo (gpt-4, claude-sonnet, etc.)
            
        Returns:
            str: Resposta da IA
        """
        if model not in self.apis:
            return f"Erro: Modelo {model} não configurado"
        
        config = self.apis[model]
        
        if not config["api_key"]:
            return f"Erro: Chave de API não configurada para {model}"
        
        try:
            if config["provider"] == "openai":
                return await self._send_openai(message, config)
            elif config["provider"] == "anthropic":
                return await self._send_anthropic(message, config)
            elif config["provider"] == "google":
                return await self._send_google(message, config)
            elif config["provider"] == "deepseek":
                return await self._send_deepseek(message, config)
            else:
                return f"Erro: Provedor {config['provider']} não implementado"
                
        except Exception as e:
            return f"Erro na API {model}: {str(e)}"
    
    async def _send_openai(self, message: str, config: Dict) -> str:
        """Envia mensagem para OpenAI GPT-4."""
        payload = {
            "model": config["model"],
            "messages": [
                {"role": "user", "content": message}
            ],
            "max_tokens": 4000,
            "temperature": 0.7
        }
        
        async with httpx.AsyncClient(timeout=120.0) as client:
            response = await client.post(
                config["base_url"],
                headers=config["headers"](config["api_key"]),
                json=payload
            )
            
            if response.status_code == 200:
                data = response.json()
                return data["choices"][0]["message"]["content"]
            else:
                return f"Erro HTTP {response.status_code}: {response.text}"
    
    async def _send_anthropic(self, message: str, config: Dict) -> str:
        """Envia mensagem para Anthropic Claude."""
        print(f"🔍 CLAUDE DEBUG - Mensagem enviada: {message[:200]}...")

        payload = {
            "model": config["model"],
            "max_tokens": 4000,
            "messages": [
                {"role": "user", "content": message}
            ],
            "temperature": 0.7,
            "top_p": 0.9
        }
        
        async with httpx.AsyncClient(timeout=120.0) as client:
            response = await client.post(
                config["base_url"],
                headers=config["headers"](config["api_key"]),
                json=payload
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Verificar estrutura da resposta
                if "content" in data and len(data["content"]) > 0:
                    content = data["content"][0]
                    if "text" in content:
                        return content["text"]
                    else:
                        return f"❌ Claude: Conteúdo sem texto. Tipo: {content.get('type', 'desconhecido')}"
                else:
                    return f"❌ Claude: Resposta vazia. Dados: {data}"
            else:
                error_detail = ""
                try:
                    error_data = response.json()
                    error_detail = error_data.get("error", {}).get("message", response.text)
                except:
                    error_detail = response.text
                
                return f"❌ Claude HTTP {response.status_code}: {error_detail}"
    
    async def _send_google(self, message: str, config: Dict) -> str:
        """Envia mensagem para Google Gemini com retry automático."""
        url = f"{config['base_url']}?key={config['api_key']}"

        print(f"[GEMINI DEBUG] URL: {url[:100]}...")
        print(f"[GEMINI DEBUG] Enviando mensagem: {message[:50]}...")

        # Retry automático para sobrecarga
        max_retries = 3
        for attempt in range(max_retries):
            if attempt > 0:
                print(f"[GEMINI DEBUG] Tentativa {attempt + 1}/{max_retries}...")
                await asyncio.sleep(2 ** attempt)  # Backoff exponencial: 2s, 4s, 8s

            payload = {
            "contents": [{
                "parts": [{
                    "text": message
                }]
            }],
            "generationConfig": {
                "temperature": 0.7,
                "maxOutputTokens": 4000,
                "topK": 40,
                "topP": 0.95
            },
            "safetySettings": [
                {
                    "category": "HARM_CATEGORY_HARASSMENT",
                    "threshold": "BLOCK_NONE"
                },
                {
                    "category": "HARM_CATEGORY_HATE_SPEECH", 
                    "threshold": "BLOCK_NONE"
                },
                {
                    "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    "threshold": "BLOCK_NONE"
                },
                {
                    "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                    "threshold": "BLOCK_NONE"
                }
            ]
        }
        
            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    url,
                    headers=config["headers"](config["api_key"]),
                    json=payload
                )

                print(f"[GEMINI DEBUG] Status: {response.status_code}")

                if response.status_code == 200:
                    data = response.json()
                    print(f"[GEMINI DEBUG] Response keys: {list(data.keys())}")

                    # Verificar se há candidatos válidos
                    if "candidates" in data and len(data["candidates"]) > 0:
                        candidate = data["candidates"][0]
                        print(f"[GEMINI DEBUG] Candidate keys: {list(candidate.keys())}")

                        # Verificar se foi bloqueado por filtros de segurança
                        if "finishReason" in candidate and candidate["finishReason"] == "SAFETY":
                            return "❌ Gemini: Resposta bloqueada por filtros de segurança. Tente reformular a pergunta."

                        # Extrair conteúdo
                        if "content" in candidate and "parts" in candidate["content"]:
                            text = candidate["content"]["parts"][0]["text"]
                            print(f"[GEMINI DEBUG] Resposta: {text[:100]}...")
                            return text
                        else:
                            print(f"[GEMINI DEBUG] Candidate completo: {candidate}")
                            return "❌ Gemini: Resposta vazia ou malformada."
                    else:
                        print(f"[GEMINI DEBUG] Dados completos: {data}")
                        return f"❌ Gemini: Nenhum candidato retornado. Dados: {data}"
                else:
                    error_text = response.text
                    print(f"[GEMINI DEBUG] Erro: {error_text}")

                    # Se for erro 503 (overloaded), tentar novamente
                    if response.status_code == 503 and attempt < max_retries - 1:
                        print(f"[GEMINI DEBUG] Modelo sobrecarregado, tentando novamente em {2 ** (attempt + 1)}s...")
                        continue

                    return f"❌ Gemini HTTP {response.status_code}: {error_text}"

        # Se chegou aqui, todas as tentativas falharam
        return "❌ Gemini: Modelo sobrecarregado após múltiplas tentativas. Tente novamente em alguns minutos."
    
    async def _send_deepseek(self, message: str, config: Dict) -> str:
        """Envia mensagem para DeepSeek V3."""
        payload = {
            "model": config["model"],
            "messages": [
                {"role": "user", "content": message}
            ],
            "max_tokens": 4000,
            "temperature": 0.7,
            "stream": False
        }
        
        async with httpx.AsyncClient(timeout=120.0) as client:
            response = await client.post(
                config["base_url"],
                headers=config["headers"](config["api_key"]),
                json=payload
            )
            
            if response.status_code == 200:
                data = response.json()
                return data["choices"][0]["message"]["content"]
            else:
                return f"Erro HTTP {response.status_code}: {response.text}"


# Instância global
unified_ai = UnifiedAI()


# Teste básico se executado diretamente
if __name__ == "__main__":
    async def test_apis():
        print("=" * 60)
        print("🧪 TESTANDO UNIFIED AI")
        print("=" * 60)
        
        print(f"📋 Modelos disponíveis: {unified_ai.get_available_external_models()}")
        
        # Teste com cada API disponível
        test_message = "Olá! Você pode me explicar brevemente o que é inteligência artificial?"
        
        for model in unified_ai.get_available_external_models():
            print(f"\n🤖 Testando {model}...")
            try:
                response = await unified_ai.send_message(test_message, model)
                print(f"✅ {model}: {response[:100]}...")
            except Exception as e:
                print(f"❌ {model}: Erro - {e}")
        
        print("\n✅ Teste concluído!")
        print("=" * 60)
    
    try:
        asyncio.run(test_apis())
    except Exception as e:
        print(f"❌ ERRO NO TESTE: {e}")