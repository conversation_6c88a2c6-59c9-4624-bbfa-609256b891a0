
#!/usr/bin/env python3
"""
Lilith - Sistema principal que gerencia todos os módulos
"""

import sys
import os

# Define diretório correto
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)  # Muda para o diretório do script
sys.path.insert(0, script_dir)  # Adiciona ao Python path

print(f"🔍 Diretório atual: {os.getcwd()}")

def show_menu():
    """Exibe o menu principal"""
    print("=" * 50)
    print("LILITH - Sistema Principal")
    print("=" * 50)
    print("1. Iniciar Painel Enterprise")
    print("2. Iniciar Stable Diffusion WebUI")
    print("3. Iniciar Tudo (Painel + SD)")
    print("4. Sair")
    print("=" * 50)

def start_painel_direct():
    """Inicia o painel diretamente sem importar"""
    print("\n🚀 Iniciando Painel Enterprise...")
    try:
        # Importa e executa diretamente
        from painel.painel import start_painel
        start_painel()
    except Exception as e:
        print(f"❌ Erro ao iniciar painel: {e}")
        print("💡 Certifique-se de que as dependências estão instaladas:")
        print("   pip install fastapi uvicorn requests")
        sys.exit(1)

def main():
    """Função principal do sistema Lilith"""
    # Verificar se foi passado argumento
    if len(sys.argv) > 1:
        choice = sys.argv[1]
    else:
        # Modo interativo
        try:
            show_menu()
            choice = input("Escolha uma opção: ").strip()
        except (EOFError, KeyboardInterrupt):
            print("\n\n🚀 Iniciando Painel Enterprise por padrão...")
            choice = "1"
    
    # Processar escolha
    if choice == "1" or choice == "painel":
        start_painel_direct()
    elif choice == "2" or choice == "sd":
        print("\n🎨 Iniciando Stable Diffusion WebUI...")
        # Inicia SD em processo separado para não quebrar
        import subprocess
        sd_path = os.path.join(script_dir, "painel", "chat1", "stable-diffusion-webui")
        if os.path.exists(sd_path):
            os.chdir(sd_path)
            subprocess.Popen([sys.executable, "launch.py", "--api", "--listen", "--port", "7860", "--autolaunch"])
            print("✅ Stable Diffusion iniciado em http://localhost:7860")
            os.chdir(script_dir)
        else:
            print("❌ Stable Diffusion não encontrado em:", sd_path)
    elif choice == "3" or choice == "all":
        print("\n🚀 Iniciando Painel + Stable Diffusion...")
        # Inicia SD primeiro
        import subprocess
        import time
        
        sd_path = os.path.join(script_dir, "painel", "chat1", "stable-diffusion-webui")
        if os.path.exists(sd_path):
            print("🎨 Iniciando Stable Diffusion...")
            os.chdir(sd_path)
            sd_process = subprocess.Popen([sys.executable, "launch.py", "--api", "--listen", "--port", "7860", "--autolaunch"])
            os.chdir(script_dir)
            time.sleep(3)  # Aguarda SD iniciar
            
        # Depois inicia o painel
        start_painel_direct()
    elif choice == "4" or choice == "quit":
        print("Saindo do sistema Lilith...")
        sys.exit(0)
    else:
        print("❌ Opção inválida!")
        print("💡 Uso: python lilith.py [1|painel|2|sd|3|all|4|quit]")
        print("   Ou execute sem argumentos para menu interativo")
        sys.exit(1)

if __name__ == "__main__":
    main()
