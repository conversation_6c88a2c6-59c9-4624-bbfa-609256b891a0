// Arquivo de exemplo JavaScript para Monaco Editor
// Demonstra syntax highlighting e funcionalidades

class MonacoExample {
    constructor(name) {
        this.name = name;
        this.version = '1.0.0';
        this.features = [
            'syntax highlighting',
            'autocompletion',
            'code folding',
            'minimap'
        ];
    }
    
    /**
     * Função de demonstração
     * @param {string} message - Mensagem a ser exibida
     * @returns {boolean} - Status da operação
     */
    showMessage(message) {
        console.log(`[${this.name}] ${message}`);
        return true;
    }
    
    // Método assíncrono
    async loadData() {
        try {
            const response = await fetch('/api/data');
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Erro ao carregar dados:', error);
            return null;
        }
    }
    
    // Método com arrow function
    processArray = (arr) => {
        return arr
            .filter(item => item !== null)
            .map(item => item.toString())
            .sort();
    }
}

// Uso da classe
const example = new MonacoExample('Lilith Monaco');
example.showMessage('Monaco Editor funcionando!');

// Demonstração de diferentes tipos de dados
const testData = {
    numbers: [1, 2, 3, 4, 5],
    strings: ['hello', 'world', 'monaco'],
    boolean: true,
    nested: {
        deep: {
            value: 'nested value'
        }
    }
};

console.log('Dados processados:', example.processArray(testData.strings));