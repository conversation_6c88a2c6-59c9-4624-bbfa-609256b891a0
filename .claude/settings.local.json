{"permissions": {"allow": ["<PERSON><PERSON>(curl:*)", "Bash(systemctl status:*)", "<PERSON><PERSON>(python test:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(python:*)", "Bash(lsof:*)", "Bash(kill:*)", "Bash(echo $OLLAMA_HOST)", "Bash(pip install:*)", "<PERSON><PERSON>(source:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(ss:*)", "Bash(venv_ai/bin/pip install httpx python-dotenv)", "Bash(cp:*)", "<PERSON><PERSON>(true)", "Bash(grep:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(rm:*)"], "deny": []}}